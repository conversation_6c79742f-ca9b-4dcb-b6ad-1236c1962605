{"name": "diagnostics", "version": "0.1.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "diagnostics", "version": "0.1.0", "dependencies": {"@fluentui/react-components": "^9.67.0", "@fluentui/react-theme": "^9.1.24", "react": "^18.3.1", "react-dom": "^18.3.1", "web-vitals": "^5.0.3"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-test-renderer": "^18.3.1", "@vitejs/plugin-react": "^4.7.0", "jsdom": "^26.1.0", "react-test-renderer": "^18.3.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "engines": {"node": ">=22", "npm": ">=10"}}, "node_modules/@ampproject/remapping": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@asamuzakjp/css-color": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.2.0.tgz", "integrity": "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==", "dev": true, "license": "MIT", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}}, "node_modules/@asamuzakjp/css-color/node_modules/lru-cache": {"version": "10.4.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "dev": true, "license": "ISC"}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz", "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz", "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator/node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "license": "ISC"}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.1", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@csstools/color-helpers": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.2.tgz", "integrity": "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT-0", "engines": {"node": ">=18"}}, "node_modules/@csstools/css-calc": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.4.tgz", "integrity": "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-color-parser": {"version": "3.0.10", "resolved": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.10.tgz", "integrity": "sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "dependencies": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}, "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-parser-algorithms": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.5.tgz", "integrity": "sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-tokenizer": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz", "integrity": "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@ctrl/tinycolor": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/@emotion/hash": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==", "license": "MIT"}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz", "integrity": "sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@floating-ui/core": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz", "integrity": "sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "peerDependencies": {"@floating-ui/dom": ">=1.5.4"}}, "node_modules/@floating-ui/dom": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz", "integrity": "sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.2", "@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/utils": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz", "integrity": "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==", "license": "MIT"}, "node_modules/@fluentui/keyboard-keys": {"version": "9.0.8", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-keys/-/keyboard-keys-9.0.8.tgz", "integrity": "sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/priority-overflow": {"version": "9.1.15", "resolved": "https://registry.npmjs.org/@fluentui/priority-overflow/-/priority-overflow-9.1.15.tgz", "integrity": "sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react-accordion": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-accordion/-/react-accordion-9.8.0.tgz", "integrity": "sha512-YHvTZCdARlDKF69qt3nQc+Q4N3uFTUDmZGg97/H+HZAbpTlwnQVsw//y860M828d5SMyvutNm6BGqpsb+XBudw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-accordion/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-accordion/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-alert": {"version": "9.0.0-beta.124", "resolved": "https://registry.npmjs.org/@fluentui/react-alert/-/react-alert-9.0.0-beta.124.tgz", "integrity": "sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==", "license": "MIT", "dependencies": {"@fluentui/react-avatar": "^9.6.29", "@fluentui/react-button": "^9.3.83", "@fluentui/react-icons": "^2.0.239", "@fluentui/react-jsx-runtime": "^9.0.39", "@fluentui/react-tabster": "^9.21.5", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.10", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-aria": {"version": "9.15.4", "resolved": "https://registry.npmjs.org/@fluentui/react-aria/-/react-aria-9.15.4.tgz", "integrity": "sha512-5t/BrCQOWz3ZAbCy6RHN3iT3+MiwbHe3ESZXoxSquxVJzBjDixuvzhnls83cqC86OaWi2fp2kI8e3/BvLA54+Q==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-avatar": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-avatar/-/react-avatar-9.9.0.tgz", "integrity": "sha512-2KWRkz7khP42ROD/thdID+dHhyCz8irQp37pD3pyLRAZe7Su1ckkjbaSB3aBl3ee0rmVq8vQmyulshsGZkyFJg==", "license": "MIT", "dependencies": {"@fluentui/react-badge": "^9.4.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-avatar/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-avatar/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-badge": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-badge/-/react-badge-9.4.0.tgz", "integrity": "sha512-FS12bACA0i5YFwTjYT1aF0NBSoNgPdZTNXM/MqJpqOq6UyCylRf75ro06a0LduU671gB578Ap+yzk8E3+Ia9NQ==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-breadcrumb": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-breadcrumb/-/react-breadcrumb-9.3.0.tgz", "integrity": "sha512-t8EAbhyO/wFJAzEr921Oag0yrkKcX6zprqzJ1dybWv8ndyjbJdQcut0fkOeMwmXCgu3MoBirW27s+4gHODwidw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-link": "^9.6.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-button": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-button/-/react-button-9.6.0.tgz", "integrity": "sha512-rsSGqJrXs4NL8Lo/2BCDEGYZrGj3Kkg2crVYnG3xBC2GMFGmlww+ovsIUcWhMo6KRY87F8dyqUcqX1g+HJNv/A==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-card": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-card/-/react-card-9.4.0.tgz", "integrity": "sha512-hH862zMzVVS1BRE2UGH8ZrLT0z1yLg4LRn4L8onEfCAKj5E65o+trGH4T6c0TOLexNyJKeF6bqrQDUtbT35pIA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-text": "^9.6.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-carousel": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-carousel/-/react-carousel-9.8.0.tgz", "integrity": "sha512-6BRHOSzaY7gkSvktaHBfa3FE/Tdmjel0o1lrR0Zl1D0kdbUDtY8ICb0FtROJ4YLSE2YyLWmAMlR3MbxKWPmCcw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-fade": "^8.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-carousel/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-carousel/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-checkbox": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-checkbox/-/react-checkbox-9.5.0.tgz", "integrity": "sha512-HB4zac4C0Msqbrjl7AOTuEMnmpEyKeNTaKc8eb9MDU8xJVWzWS5Q91TWpmXOXgneaG3/pu5ops749zBmlCU1Pg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-color-picker": {"version": "9.2.0", "resolved": "https://registry.npmjs.org/@fluentui/react-color-picker/-/react-color-picker-9.2.0.tgz", "integrity": "sha512-4E6woOMxj4Tyy0sHAORR8pGUlZbtoGgQ6UsdQ38SWEU+f/zo/2SsyJOqtuMur67+ThAoJR5bq+W3mLGi8WhAPA==", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.3.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-color-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-color-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-combobox": {"version": "9.16.0", "resolved": "https://registry.npmjs.org/@fluentui/react-combobox/-/react-combobox-9.16.0.tgz", "integrity": "sha512-w84o5ubLL4MCfbzb/xCRoWjc1S2ZGk0Ci8PEXkP+CFAl3SxAORJISAiMCbfk+ZoWAwNLFcHNO6UFj2XH+fWkbA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-combobox/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-combobox/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-components": {"version": "9.67.0", "resolved": "https://registry.npmjs.org/@fluentui/react-components/-/react-components-9.67.0.tgz", "integrity": "sha512-692/t+6te3HO0/tA5585CrA9FA6AIR7a6UVJ/p6Cah0cwRfi/ffeNYZ5fhZHX/46DU0SRfAXemcsoFE1cgKpYA==", "license": "MIT", "dependencies": {"@fluentui/react-accordion": "^9.8.0", "@fluentui/react-alert": "9.0.0-beta.124", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-badge": "^9.4.0", "@fluentui/react-breadcrumb": "^9.3.0", "@fluentui/react-button": "^9.6.0", "@fluentui/react-card": "^9.4.0", "@fluentui/react-carousel": "^9.8.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-color-picker": "^9.2.0", "@fluentui/react-combobox": "^9.16.0", "@fluentui/react-dialog": "^9.14.0", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-drawer": "^9.9.0", "@fluentui/react-field": "^9.4.0", "@fluentui/react-image": "^9.3.0", "@fluentui/react-infobutton": "9.0.0-beta.102", "@fluentui/react-infolabel": "^9.4.0", "@fluentui/react-input": "^9.7.0", "@fluentui/react-label": "^9.3.0", "@fluentui/react-link": "^9.6.0", "@fluentui/react-list": "^9.3.0", "@fluentui/react-menu": "^9.19.0", "@fluentui/react-message-bar": "^9.6.0", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-nav": "^9.3.0", "@fluentui/react-overflow": "^9.5.0", "@fluentui/react-persona": "^9.5.0", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-progress": "^9.4.0", "@fluentui/react-provider": "^9.22.0", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-rating": "^9.3.0", "@fluentui/react-search": "^9.3.0", "@fluentui/react-select": "^9.4.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-skeleton": "^9.4.0", "@fluentui/react-slider": "^9.5.0", "@fluentui/react-spinbutton": "^9.5.0", "@fluentui/react-spinner": "^9.7.0", "@fluentui/react-swatch-picker": "^9.4.0", "@fluentui/react-switch": "^9.4.0", "@fluentui/react-table": "^9.18.0", "@fluentui/react-tabs": "^9.9.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-tag-picker": "^9.7.0", "@fluentui/react-tags": "^9.7.0", "@fluentui/react-teaching-popover": "^9.6.0", "@fluentui/react-text": "^9.6.0", "@fluentui/react-textarea": "^9.6.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-toast": "^9.6.0", "@fluentui/react-toolbar": "^9.6.0", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-tree": "^9.12.0", "@fluentui/react-utilities": "^9.22.0", "@fluentui/react-virtualizer": "9.0.0-alpha.100", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-dialog": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/@fluentui/react-dialog/-/react-dialog-9.14.0.tgz", "integrity": "sha512-FgvxWVwET9niVhWoD1gpEx7MICOCDncTyreJV12KmCVC0eYxvun0uQmA6FXVnh+3yh/9AhIH0KfiKa0C8qsP7g==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-dialog/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-dialog/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-divider": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-divider/-/react-divider-9.4.0.tgz", "integrity": "sha512-WLs/12FP7Yf+SYCISzxGaNbLvJjZyBcUFbG9KhhRmt5CcwIklTinEJWW3qXcAmS+nHuGdkwpgC/avgEjzpYMcg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-drawer": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-drawer/-/react-drawer-9.9.0.tgz", "integrity": "sha512-HjW13Tikmk7s/XUKGoYn6MKsvm9gmO6Og8h3PtcWIccsXBUesQtWAgNJpgvprEDKHFwLF5aB1fHqYDsStbrLCw==", "license": "MIT", "dependencies": {"@fluentui/react-dialog": "^9.14.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-field": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-field/-/react-field-9.4.0.tgz", "integrity": "sha512-X4XWe1gWVxUP6Oa395Ekpdtj9FX2WAWPj5+DGW8OGB7SNJA67cEP/E8FCEA/tflm0eZXaHVFThh0yElf1KX7nw==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-field/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-field/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-icons": {"version": "2.0.304", "resolved": "https://registry.npmjs.org/@fluentui/react-icons/-/react-icons-2.0.304.tgz", "integrity": "sha512-8Pooy3e7NZUBcvBEpJUy1xmapS1ouT9s42rcNgOvou4wmcv10n+iFtsUUHswJ9Zr8F/gn1V0YcA2SqtFbu7P8Q==", "license": "MIT", "dependencies": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-image": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-image/-/react-image-9.3.0.tgz", "integrity": "sha512-qhKZ6Dj267UPvnAwzmvLD3JDb8zSCEtkL2c9CLyUAcuuvT4KubhNsLudY//1EMiC5a+Du0gC2lcxRT84PQ2NZg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infobutton": {"version": "9.0.0-beta.102", "resolved": "https://registry.npmjs.org/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.102.tgz", "integrity": "sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.237", "@fluentui/react-jsx-runtime": "^9.0.36", "@fluentui/react-label": "^9.1.68", "@fluentui/react-popover": "^9.9.6", "@fluentui/react-tabster": "^9.21.0", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.7", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-infolabel": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-infolabel/-/react-infolabel-9.4.0.tgz", "integrity": "sha512-ABSzkV/FN0TfKRXbarb+/dWihgKpqDeS5YWf69pCeXg7s+Ls3UQn/7+mgBjHcMOoRpbqW45bOzCoC+6Iqb2ggg==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-input": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-input/-/react-input-9.7.0.tgz", "integrity": "sha512-rJCVaVnAidVtp//DQFaz1vHMbiNVcxZPjvZ9xfIpdRjFk+kSEkcRj1AT/iCMqwTXhJb9hYIMJRE+gPQoTiQYdQ==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-jsx-runtime": {"version": "9.1.2", "resolved": "https://registry.npmjs.org/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.1.2.tgz", "integrity": "sha512-igGuh0P7Gd09Kk3g6JwjnaRIRk+mluCbpf+KcAUde6bxZ/5qB50HGX+DOGWa3+RPd5240+HLBxpT3Y985INgqw==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-label": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-label/-/react-label-9.3.0.tgz", "integrity": "sha512-H<PERSON>i4TBEjkJoeNZ9FOL8VPnOwrKrJp5drd1f00cICwRz7cimSZt56C97BwM9IB41nEdF3Yk3MLd4Hea1PO+Msg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-link": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-link/-/react-link-9.6.0.tgz", "integrity": "sha512-2G+IWuT98pt1HwJWuL9VuTQesUdYjDooK/LPUOsXaVwwGP71lKBXQ6B7ZBw5bqDt3dwborTugyG6RlD7aDpPbw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-list": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-list/-/react-list-9.3.0.tgz", "integrity": "sha512-OsYz2ULKXnFEExZW8FaUk1+cjPcFIrtRlwytKDAnRvwyBLIhhQezRWWTEVpc2M75NmZbkZtqyDujdB/ZdSlOmA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-list/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-list/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-menu": {"version": "9.19.0", "resolved": "https://registry.npmjs.org/@fluentui/react-menu/-/react-menu-9.19.0.tgz", "integrity": "sha512-Wy/8DaHXEtntJk2onVWZI19AHIJkAJB9gXjXrKFk4DbSX0n9Brj06dBu9lZzl5q4i7cUQhg9sMayle3ovspX6w==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-menu/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-menu/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-message-bar": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-message-bar/-/react-message-bar-9.6.0.tgz", "integrity": "sha512-sGVd+wK2NsiHBcGl1Pw3P4LJW50hbaN4+4NA5udCwbtIW97lO2zMFJtROU+oBYkmV0HbJ9jSxOYyeMmndjKjAQ==", "license": "MIT", "dependencies": {"@fluentui/react-button": "^9.6.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-link": "^9.6.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-motion": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-motion/-/react-motion-9.9.0.tgz", "integrity": "sha512-xgm/CkU1UvemooplEFKJL9mfGJFvzId2DJ1WYTFAa5TSZMtzOAZuPuwS/PrPNFuwjnhvCMShDj8zazgvR5i37A==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-motion-components-preview": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-motion-components-preview/-/react-motion-components-preview-0.7.0.tgz", "integrity": "sha512-vGxi2KLqwCzfV2WSZBYGKSzKnfsnGKjkQpE5qYfwk0aPp3iDXtyiLCANgNiPKIBJ4R+/48SAbIDKaiXBtd7GRw==", "license": "MIT", "dependencies": {"@fluentui/react-motion": "*", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-nav": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-nav/-/react-nav-9.3.0.tgz", "integrity": "sha512-IodGcAPlH45pNskmPmFsXF8IGGrRAEcd4PrytdAPFhBx0Ov69uvoI1B7mCTDGYYb0g8KRW751rGJtU4QMgUAUw==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-drawer": "^9.9.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-nav/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-nav/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-overflow": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-overflow/-/react-overflow-9.5.0.tgz", "integrity": "sha512-XIJ2WGNiSs4KER5GIV9iMQA/lGVSR2eE+Aeht+hGiwlmn/YvTvS5SM/LSw2CKyi1LkVRzNB3Kj1wiIzD/he5+Q==", "license": "MIT", "dependencies": {"@fluentui/priority-overflow": "^9.1.15", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-overflow/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-overflow/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-persona": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-persona/-/react-persona-9.5.0.tgz", "integrity": "sha512-0MnNTqrJ3BxTXvg+NdLE9mabSmLFVKiuqdIAtK/gYFiEk43wGskMUx9Kw1Dfq6xRYQImaFnoLhd+47YsLyn9jg==", "license": "MIT", "dependencies": {"@fluentui/react-avatar": "^9.9.0", "@fluentui/react-badge": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-popover": {"version": "9.12.0", "resolved": "https://registry.npmjs.org/@fluentui/react-popover/-/react-popover-9.12.0.tgz", "integrity": "sha512-qnPwYW3E63jLTaVB7ssbTVE9ez04eNmky7SjdD2MlU6F2506nuV5V7wPp3Z5LZpD6SQqgMjtPiTlcFgWHAjAvw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-popover/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-popover/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-portal": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-portal/-/react-portal-9.7.0.tgz", "integrity": "sha512-g9Q9wsw4OH4UFYyjb5BfbL7GwaloIiFMVZXie9q0lLeo9JUFhNHh/2X7UUGesagCO86WMGN1haQUA7uaN6gIXA==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-positioning": {"version": "9.20.0", "resolved": "https://registry.npmjs.org/@fluentui/react-positioning/-/react-positioning-9.20.0.tgz", "integrity": "sha512-qbxIYG8N+zBVXsgyiqd8kQzDiEn+eabnDBn3hqhaolVqn3QVWfgjoARJXYuKbUY0GDMPMukW1PH2NbEl5BvQXQ==", "license": "MIT", "dependencies": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.6.12", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-progress": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-progress/-/react-progress-9.4.0.tgz", "integrity": "sha512-EplT3K95DPob22MV0mIzLmbzsdS2bhMPEiRjUAsRpUPnw5gRJi4OKneS5y3mRCBUiFjlkzEDwTTbEa+NkZEXlg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-provider": {"version": "9.22.0", "resolved": "https://registry.npmjs.org/@fluentui/react-provider/-/react-provider-9.22.0.tgz", "integrity": "sha512-dyrux/z+OXTM9U0uaq/AHtSI/5jZsehw3LND79StMP11ebi9lGjyRthZ3M8E6Pq7LlSgQ0yVnMFYZc9WoijVHg==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/core": "^1.16.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-radio": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-radio/-/react-radio-9.5.0.tgz", "integrity": "sha512-9j4t85KdIYu5TN3tN1S2KlIfzL4FNYRuFBsQ8WxB0F8vmGlyIxUt9S2dRG3+MScqOwIS2Q0HAmZhu0hrTJVWRg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-rating": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-rating/-/react-rating-9.3.0.tgz", "integrity": "sha512-FP19VCBG3aQm7uP/pORfDBKHU/f5YinvETe39y4+9VPiXlgbF+sqjwXGB6N7kvu9ZdTD4ZFrMW4FaSLYrpJEtA==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-search": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-search/-/react-search-9.3.0.tgz", "integrity": "sha512-RMzYhNdrLpz5/e6Z3NlDmX7KP+AXz0N0e4SBoKjHauoDfEPD9+oHbSbah/JQWmw290h1jUUrElRwPYoIQ8eSgg==", "license": "MIT", "dependencies": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-input": "^9.7.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-select": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-select/-/react-select-9.4.0.tgz", "integrity": "sha512-6DoC6Xc6hkHKCzRFjB2UYbJAa3v+KZ/OUML18OvYvdGkEtv+n2x3sc+mUDgFuXHqB/4OIhUDXq4S/Mriwd8KUg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-shared-contexts": {"version": "9.24.0", "resolved": "https://registry.npmjs.org/@fluentui/react-shared-contexts/-/react-shared-contexts-9.24.0.tgz", "integrity": "sha512-GA+uLv711E+YGrAP/aVB15ozvNCiuB2ZrPDC9aYF+A6sRDxoZZG8VgHjhQ/YWJfVjDXLky4ihirknzsW1sjGtg==", "license": "MIT", "dependencies": {"@fluentui/react-theme": "^9.1.24", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-skeleton": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-skeleton/-/react-skeleton-9.4.0.tgz", "integrity": "sha512-n6viQkyI+g7ljf33x/6FVwNfyfJq6Qosug5OlxsSTrneyn+kSb6lw8K4z3AUIEBOR65XEonYWegXOm4ldcJYOw==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-slider": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-slider/-/react-slider-9.5.0.tgz", "integrity": "sha512-qxLRYBKKEbRuKdHzE0iSpETvjYKGjIK4Rm18swFd5Jl4SfXUxaq6EuHRE1sfiOhraH2nDSKHVT+iXZxYi/g+Tg==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinbutton": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-spinbutton/-/react-spinbutton-9.5.0.tgz", "integrity": "sha512-rRdgwNb0yNJOeCwbr6Kn1VX+ys+4PEfl6bwHphXy/6iwbF7BETtZjmGGbfXhuu+WsLxQxHnyeo5uC21E/mbWqg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-spinner": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-spinner/-/react-spinner-9.7.0.tgz", "integrity": "sha512-B9KQ6Muy2KZIBpmzkdZ0ONu4Ao/3iMhBous1Emq7wfiYEhoz1pOLKvVgh+IgXz5SX28x8cZiDt9/Hu7Quf6zJg==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-swatch-picker": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-swatch-picker/-/react-swatch-picker-9.4.0.tgz", "integrity": "sha512-KSeIvU/fwBeXP5irqQxSvs34LNu03a3NYF48GOJrDODUwv/tjYn+/IgsPRMjA2pZ502AMWFa5OSKpeUJ9mbi1g==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-swatch-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-swatch-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-switch": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-switch/-/react-switch-9.4.0.tgz", "integrity": "sha512-8uKP2aM/doLGprYuljbJAbAapeVWbgMW1FLQH53+RHURZNy1Gvt8AiisllJwtmQC8esgK4xlbaSzn/b1/S8B8A==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-table": {"version": "9.18.0", "resolved": "https://registry.npmjs.org/@fluentui/react-table/-/react-table-9.18.0.tgz", "integrity": "sha512-yBdBvY5X/XnX5WYoFseKlqc0pYomBZ+3jFaMEeWMYxOAIuHWif3IUq4kTxBoweKcFMmclMNMpY22j/6YcFwHXA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-table/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-table/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tabs": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tabs/-/react-tabs-9.9.0.tgz", "integrity": "sha512-V06heimvtH5LcjjePkl8ETWrX4YN1V2STQhFr6lXn6FjS8nsNGhWemHduCi2qY3DLyZgYLBGrOR5AgSbbv5jcA==", "license": "MIT", "dependencies": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tabs/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tabs/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tabster": {"version": "9.26.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tabster/-/react-tabster-9.26.0.tgz", "integrity": "sha512-ENaISUye53JLvAN3VqiKzTdDSubnMucG/mcGuB+QbnzTLGIHxvEYq/GV4WHwWbQwjZPXAG9Hr0F0l0AFzrkeFA==", "license": "MIT", "dependencies": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "keyborg": "^2.6.0", "tabster": "^8.5.5"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tag-picker": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tag-picker/-/react-tag-picker-9.7.0.tgz", "integrity": "sha512-p0xAxemN/fYlDG6dVbkcGybjMCiNravyzTsnpE2OwEoh3eDfsL5oXipPkcJACzv5ZhmavVyAHs4txenEcW24gw==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-combobox": "^9.16.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-tags": "^9.7.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tag-picker/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tag-picker/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tags": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tags/-/react-tags-9.7.0.tgz", "integrity": "sha512-TU7CPouGFuOXxGVjrbWbLgyTNrVoyxOS3xvwdZGJuGlaU9FbFuzKNUeV/CL0o6SiA/0O1wGa4/VV6XRuUGQX3Q==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-teaching-popover": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-teaching-popover/-/react-teaching-popover-9.6.0.tgz", "integrity": "sha512-/JX1+W/ff8bkO1nCSExL9ASP1zfysUInc83V/6XzRgwhyNMkUoNgGRw32EDpxz6Ympe8mZnQKWNUmvTsxr28aQ==", "license": "MIT", "dependencies": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}, "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/@fluentui/react-teaching-popover/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-teaching-popover/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-text": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-text/-/react-text-9.6.0.tgz", "integrity": "sha512-/ZAMjgAn5sgbAjYnwmM5k0kxgNehpccxXI6f5uJ72IfAmj85dMH4TDNsN6xOCIMhj+xDxuBIT4axEYt+wAoF1Q==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-textarea": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-textarea/-/react-textarea-9.6.0.tgz", "integrity": "sha512-o6jAAB4cIzzPLBj8/RDo+my7yXSQtFCC+O2p4mD2X+hUvBCydoQI+45RbEeJXXwEsWjUp7XfbLUyt3mB8dH0xQ==", "license": "MIT", "dependencies": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-theme": {"version": "9.1.24", "resolved": "https://registry.npmjs.org/@fluentui/react-theme/-/react-theme-9.1.24.tgz", "integrity": "sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==", "license": "MIT", "dependencies": {"@fluentui/tokens": "1.0.0-alpha.21", "@swc/helpers": "^0.5.1"}}, "node_modules/@fluentui/react-toast": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-toast/-/react-toast-9.6.0.tgz", "integrity": "sha512-t/eUl3w8RdLFMLHcvWHXCH9jec29MV7K7pqmyXsW2g7edaChTyCbkxlII861IvY+XqwIvNlpczzh4cgkyzAj/w==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-toolbar": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-toolbar/-/react-toolbar-9.6.0.tgz", "integrity": "sha512-tbpM8prz8cDTzeF7PudjTA3UQruVrjGNSsTwR+vmIGVo0E986Zz+VSJaLICeC2ttiHOirhqm6goswP+bGG5Evw==", "license": "MIT", "dependencies": {"@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-toolbar/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-toolbar/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-tooltip": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tooltip/-/react-tooltip-9.8.0.tgz", "integrity": "sha512-LW0ouXkPXxx+XPScLB9tWlqn11cVHxmDJ3weZXuWrl5jjx4agjqKHGC8MOdr4Un+2hoO0g2BcrlDaQNhsMPgYA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tree": {"version": "9.12.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tree/-/react-tree-9.12.0.tgz", "integrity": "sha512-vehLCk918YN53h8sGs4jx5oEF2twdVRdoIQ+csuLUkxRhl7f6eWyQWRk/R2lZlJgsz0vgk07RB/Yfx8/BFEUiA==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-button": "^9.6.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-tree/node_modules/@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "license": "MIT", "dependencies": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0", "scheduler": ">=0.19.0 <=0.23.0"}}, "node_modules/@fluentui/react-tree/node_modules/scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "license": "MIT", "peer": true, "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/@fluentui/react-utilities": {"version": "9.22.0", "resolved": "https://registry.npmjs.org/@fluentui/react-utilities/-/react-utilities-9.22.0.tgz", "integrity": "sha512-O4D51FUyn5670SjduzzN1usmwWAmFPQA00Gu6jJrbDXvOXTpOAO/ApkLpSW87HChKGrj8Y0gjFHtK8xpC3qOCg==", "license": "MIT", "dependencies": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-shared-contexts": "^9.24.0", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "react": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/react-virtualizer": {"version": "9.0.0-alpha.100", "resolved": "https://registry.npmjs.org/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.100.tgz", "integrity": "sha512-e7u3SP2Smv5+9Adey+pOerGmHq2D6Nd0ek/iWbc/o0CKX5QMeHwbUlZAbVVsrX/vwIeeZ3+qJMt+UH3hHI+wdw==", "license": "MIT", "dependencies": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "peerDependencies": {"@types/react": ">=16.14.0 <19.0.0", "@types/react-dom": ">=16.9.0 <19.0.0", "react": ">=16.14.0 <19.0.0", "react-dom": ">=16.14.0 <19.0.0"}}, "node_modules/@fluentui/tokens": {"version": "1.0.0-alpha.21", "resolved": "https://registry.npmjs.org/@fluentui/tokens/-/tokens-1.0.0-alpha.21.tgz", "integrity": "sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==", "license": "MIT", "dependencies": {"@swc/helpers": "^0.5.1"}}, "node_modules/@griffel/core": {"version": "1.19.2", "resolved": "https://registry.npmjs.org/@griffel/core/-/core-1.19.2.tgz", "integrity": "sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.3.0", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "node_modules/@griffel/react": {"version": "1.5.30", "resolved": "https://registry.npmjs.org/@griffel/react/-/react-1.5.30.tgz", "integrity": "sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==", "license": "MIT", "dependencies": {"@griffel/core": "^1.19.2", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <20.0.0"}}, "node_modules/@griffel/style-types": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@griffel/style-types/-/style-types-1.3.0.tgz", "integrity": "sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==", "license": "MIT", "dependencies": {"csstype": "^3.1.3"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "dev": true, "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.5.tgz", "integrity": "sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==", "dev": true, "optional": true, "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "node_modules/@jridgewell/source-map/node_modules/@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "dev": true, "optional": true, "peer": true, "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.27", "resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.27.tgz", "integrity": "sha512-+d0F4MKMCbeVUJwG96uQ4SgAznZNSq93I3V+9NHA4OpvqG8mRCpGdKmK8l/dl02h2CCDHwW2FqilnTyDcAnqjA==", "dev": true, "license": "MIT"}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz", "integrity": "sha512-<PERSON>rjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "integrity": "sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz", "integrity": "sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz", "integrity": "sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz", "integrity": "sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz", "integrity": "sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz", "integrity": "sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.43.0.tgz", "integrity": "sha512-ZJ3gZynL1LDSIvRfz0qXtTNs56n5DI2Mq+WACWZ7yGHFUEirHBRt7fyIk0NsCKhmRhn7WAcjgSkSVVxKlPNFFw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz", "integrity": "sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz", "integrity": "sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz", "integrity": "sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz", "integrity": "sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz", "integrity": "sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz", "integrity": "sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz", "integrity": "sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz", "integrity": "sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz", "integrity": "sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "integrity": "sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz", "integrity": "sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.43.0.tgz", "integrity": "sha512-SnGhLiE5rlK0ofq8kzuDkM0g7FN1s5VYY+YSMTibP7CqShxCQvqtNxTARS4xX4PFJfHjG0ZQYX9iGzI3FQh5Aw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@swc/helpers": {"version": "0.5.8", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.8.tgz", "integrity": "sha512-lruDGw3pnfM3wmZHeW7JuhkGQaJjPyiKjxeGhdmfoOT53Ic9qb5JLDNaK2HUdl1zLDeX28H221UvKjfdvSLVMg==", "dependencies": {"tslib": "^2.4.0"}}, "node_modules/@testing-library/dom": {"version": "10.1.0", "resolved": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.1.0.tgz", "integrity": "sha512-wdsYKy5zupPyLCW2Je5DLHSxSfbIp6h80WoHOQc+RPtmPGA52O9x5MJEkv92Sjonpq+poOAtUKhh1kBGAXBrNA==", "dev": true, "peer": true, "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "engines": {"node": ">=18"}}, "node_modules/@testing-library/react": {"version": "16.3.0", "resolved": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "integrity": "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw==", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "engines": {"node": ">=18"}, "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@testing-library/user-event": {"version": "14.6.1", "resolved": "https://registry.npmjs.org/@testing-library/user-event/-/user-event-14.6.1.tgz", "integrity": "sha512-vq7fv0rnt+QTXgPxr5Hjc210p6YKq2kmdziLgnsZGgLJ9e6VAShx1pACLuRjd/AS/sr7phAR58OIIpf0LlmQNw==", "dev": true, "license": "MIT", "engines": {"node": ">=12", "npm": ">=6"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}}, "node_modules/@types/aria-query": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz", "integrity": "sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==", "dev": true, "peer": true}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.6.4", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz", "integrity": "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.1", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz", "integrity": "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==", "dev": true, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.18.0", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.0.tgz", "integrity": "sha512-v4Vwdko+pgymgS+A2UIaJru93zQd85vIGWObM5ekZNdXCKtDYqATlEYnWgfo86Q6I1Lh0oXnksDnMU1cwmlPDw==", "dev": true, "dependencies": {"@babel/types": "^7.3.0"}}, "node_modules/@types/chai": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/@types/chai/-/chai-5.2.2.tgz", "integrity": "sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/deep-eql/-/deep-eql-4.0.2.tgz", "integrity": "sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.16.5", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.16.5.tgz", "integrity": "sha512-bJFoMATwIGaxxx8VJPeM8TonI8t579oRvgAuT8zFugJsJZgzqv0Fu8Mhp68iecjzG7cnN3mO2dJQ5uUM2EFrgQ==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/prop-types": {"version": "15.7.5", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "integrity": "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="}, "node_modules/@types/react": {"version": "18.3.23", "resolved": "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz", "integrity": "sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==", "license": "MIT", "dependencies": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "18.3.7", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz", "integrity": "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==", "license": "MIT", "peerDependencies": {"@types/react": "^18.0.0"}}, "node_modules/@types/react-test-renderer": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/@types/react-test-renderer/-/react-test-renderer-18.3.1.tgz", "integrity": "sha512-vAhnk0tG2eGa37lkU9+s5SoroCsRI08xnsWFiAXOuPH2jqzMbcXvKExXViPi1P5fIklDeCvXqyrdmipFaSkZrA==", "dev": true, "license": "MIT", "dependencies": {"@types/react": "^18"}}, "node_modules/@vitejs/plugin-react": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.7.0.tgz", "integrity": "sha512-gUu9hwfWvvEDBBmgtAowQCojwZmJ5mcLn3aufeCsitijs3+f2NsrPtlAWIR6OPiqljl96GVCUbLe0HyqIpVaoA==", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.27", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}}, "node_modules/@vitest/expect": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.4.tgz", "integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/mocker": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/mocker/-/mocker-3.2.4.tgz", "integrity": "sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "3.2.4", "estree-walker": "^3.0.3", "magic-string": "^0.30.17"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"msw": "^2.4.9", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "peerDependenciesMeta": {"msw": {"optional": true}, "vite": {"optional": true}}}, "node_modules/@vitest/pretty-format": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.4.tgz", "integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "dev": true, "license": "MIT", "dependencies": {"tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.4.tgz", "integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "3.2.4", "pathe": "^2.0.3", "strip-literal": "^3.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/snapshot": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.4.tgz", "integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "magic-string": "^0.30.17", "pathe": "^2.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.4.tgz", "integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^4.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/utils": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.4.tgz", "integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "loupe": "^3.1.4", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/acorn": {"version": "8.11.2", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz", "integrity": "sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==", "dev": true, "optional": true, "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz", "integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "peer": true, "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "peer": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aria-query": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==", "dev": true, "peer": true, "dependencies": {"dequal": "^2.0.3"}}, "node_modules/assertion-error": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.1.tgz", "integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/browserslist": {"version": "4.25.0", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true, "optional": true, "peer": true}, "node_modules/cac": {"version": "6.7.14", "resolved": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/caniuse-lite": {"version": "1.0.30001723", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz", "integrity": "sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chai": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/chai/-/chai-5.2.0.tgz", "integrity": "sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "peer": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/check-error": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/check-error/-/check-error-2.1.1.tgz", "integrity": "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "peer": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "peer": true}, "node_modules/cssstyle": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/cssstyle/-/cssstyle-4.4.0.tgz", "integrity": "sha512-W0Y2HOXlPkb2yaKrCVRjinYKciu/qSLEmK0K9mcfDei3zwlnHFEHAs/Du3cIRwPqY+J4JsiBzUjoHyc8RsJ03A==", "dev": true, "license": "MIT", "dependencies": {"@asamuzakjp/css-color": "^3.2.0", "rrweb-cssom": "^0.8.0"}, "engines": {"node": ">=18"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "license": "MIT"}, "node_modules/data-urls": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/data-urls/-/data-urls-5.0.0.tgz", "integrity": "sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==", "dev": true, "license": "MIT", "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}, "engines": {"node": ">=18"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.5.0", "resolved": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz", "integrity": "sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==", "dev": true, "license": "MIT"}, "node_modules/deep-eql": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.2.tgz", "integrity": "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/dequal": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==", "dev": true, "peer": true, "engines": {"node": ">=6"}}, "node_modules/dom-accessibility-api": {"version": "0.5.14", "resolved": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.14.tgz", "integrity": "sha512-NMt+m9zFMPZe0JcY9gN224Qvk6qLIdqex29clBvc/y75ZBX9YA9wNK3frsYvu2DI1xcCIwxwnX+TlsJ2DSOADg==", "dev": true, "peer": true}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/electron-to-chromium": {"version": "1.5.167", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.167.tgz", "integrity": "sha512-LxcRvnYO5ez2bMOFpbuuVuAI5QNeY1ncVytE/KXaL6ZNfzX1yPlAO0nSOyIHx2fVAuUprMqPs/TdVhUFZy7SIQ==", "dev": true, "license": "ISC"}, "node_modules/embla-carousel": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.6.0.tgz", "integrity": "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==", "license": "MIT"}, "node_modules/embla-carousel-autoplay": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel-autoplay/-/embla-carousel-autoplay-8.6.0.tgz", "integrity": "sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==", "license": "MIT", "peerDependencies": {"embla-carousel": "8.6.0"}}, "node_modules/embla-carousel-fade": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel-fade/-/embla-carousel-fade-8.6.0.tgz", "integrity": "sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==", "license": "MIT", "peerDependencies": {"embla-carousel": "8.6.0"}}, "node_modules/entities": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/entities/-/entities-6.0.1.tgz", "integrity": "sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==", "dev": true, "license": "MIT"}, "node_modules/esbuild": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.5.tgz", "integrity": "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/estree-walker": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0"}}, "node_modules/expect-type": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.1.tgz", "integrity": "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/globrex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/globrex/-/globrex-0.1.2.tgz", "integrity": "sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==", "dev": true}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "peer": true, "engines": {"node": ">=8"}}, "node_modules/html-encoding-sniffer": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-4.0.0.tgz", "integrity": "sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^3.1.1"}, "engines": {"node": ">=18"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", "integrity": "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==", "dev": true, "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "node_modules/jsdom": {"version": "26.1.0", "resolved": "https://registry.npmjs.org/jsdom/-/jsdom-26.1.0.tgz", "integrity": "sha512-Cvc9WUhxSMEo4McES3P7oK3QaXldCfNWp7pl2NNeiIFlCoLr3kfq9kb1fxftiwk1FLV7CvpvDfonxtzUDeSOPg==", "dev": true, "license": "MIT", "dependencies": {"cssstyle": "^4.2.1", "data-urls": "^5.0.0", "decimal.js": "^10.5.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.16", "parse5": "^7.2.1", "rrweb-cssom": "^0.8.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^5.1.1", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.1.1", "ws": "^8.18.0", "xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"canvas": "^3.0.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keyborg": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/keyborg/-/keyborg-2.6.0.tgz", "integrity": "sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA==", "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/loupe": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/loupe/-/loupe-3.1.4.tgz", "integrity": "sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==", "dev": true, "license": "MIT"}, "node_modules/lz-string": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz", "integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==", "dev": true, "peer": true, "bin": {"lz-string": "bin/bin.js"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT"}, "node_modules/nwsapi": {"version": "2.2.20", "resolved": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz", "integrity": "sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "engines": {"node": ">=0.10.0"}}, "node_modules/parse5": {"version": "7.3.0", "resolved": "https://registry.npmjs.org/parse5/-/parse5-7.3.0.tgz", "integrity": "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==", "dev": true, "license": "MIT", "dependencies": {"entities": "^6.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/pathe": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-2.0.0.tgz", "integrity": "sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==", "dev": true, "license": "MIT", "engines": {"node": ">= 14.16"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz", "integrity": "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/pretty-format": {"version": "27.5.1", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz", "integrity": "sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==", "dev": true, "peer": true, "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "dev": true, "peer": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/prop-types/node_modules/react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/react": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "dependencies": {"loose-envify": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "dependencies": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-shallow-renderer": {"version": "16.15.0", "resolved": "https://registry.npmjs.org/react-shallow-renderer/-/react-shallow-renderer-16.15.0.tgz", "integrity": "sha512-oScf2FqQ9LFVQgA73vr86xl2NaOIX73rh+YFqcOp68CWj56tSfgtGKrEbyhCj0rSijyG9M1CYprTh39fBi5hzA==", "dev": true, "dependencies": {"object-assign": "^4.1.1", "react-is": "^16.12.0 || ^17.0.0 || ^18.0.0"}, "peerDependencies": {"react": "^16.0.0 || ^17.0.0 || ^18.0.0"}}, "node_modules/react-test-renderer": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-test-renderer/-/react-test-renderer-18.3.1.tgz", "integrity": "sha512-KkAgygexHUkQqtvvx/otwxtuFu5cVjfzTCtjXLH9boS19/Nbtg84zS7wIQn39G8IlrhThBpQsMKkq5ZHZIYFXA==", "dev": true, "dependencies": {"react-is": "^18.3.1", "react-shallow-renderer": "^16.15.0", "scheduler": "^0.23.2"}, "peerDependencies": {"react": "^18.3.1"}}, "node_modules/react-test-renderer/node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "dev": true}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/rollup": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.43.0.tgz", "integrity": "sha512-wdN2Kd3Twh8MAEOEJZsuxuLKCsBEo4PVNLK6tQWAn10VhsVewQLzcucMgLolRlhFybGxfclbPeEYBaP6RvUFGg==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.43.0", "@rollup/rollup-android-arm64": "4.43.0", "@rollup/rollup-darwin-arm64": "4.43.0", "@rollup/rollup-darwin-x64": "4.43.0", "@rollup/rollup-freebsd-arm64": "4.43.0", "@rollup/rollup-freebsd-x64": "4.43.0", "@rollup/rollup-linux-arm-gnueabihf": "4.43.0", "@rollup/rollup-linux-arm-musleabihf": "4.43.0", "@rollup/rollup-linux-arm64-gnu": "4.43.0", "@rollup/rollup-linux-arm64-musl": "4.43.0", "@rollup/rollup-linux-loongarch64-gnu": "4.43.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-musl": "4.43.0", "@rollup/rollup-linux-s390x-gnu": "4.43.0", "@rollup/rollup-linux-x64-gnu": "4.43.0", "@rollup/rollup-linux-x64-musl": "4.43.0", "@rollup/rollup-win32-arm64-msvc": "4.43.0", "@rollup/rollup-win32-ia32-msvc": "4.43.0", "@rollup/rollup-win32-x64-msvc": "4.43.0", "fsevents": "~2.3.2"}}, "node_modules/rrweb-cssom": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.8.0.tgz", "integrity": "sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw==", "dev": true, "license": "MIT"}, "node_modules/rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "license": "MIT", "dependencies": {"@babel/runtime": "^7.1.2"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true, "license": "MIT"}, "node_modules/saxes": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz", "integrity": "sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=v12.22.7"}}, "node_modules/scheduler": {"version": "0.23.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "dependencies": {"loose-envify": "^1.1.0"}}, "node_modules/siginfo": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz", "integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==", "dev": true, "license": "ISC"}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "optional": true, "peer": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "optional": true, "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/stackback": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz", "integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==", "dev": true, "license": "MIT"}, "node_modules/std-env": {"version": "3.9.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz", "integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==", "dev": true, "license": "MIT"}, "node_modules/strip-literal": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-literal/-/strip-literal-3.0.0.tgz", "integrity": "sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^9.0.1"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/strip-literal/node_modules/js-tokens": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "dev": true, "license": "MIT"}, "node_modules/stylis": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.3.6.tgz", "integrity": "sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ==", "license": "MIT"}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "peer": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/symbol-tree": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz", "integrity": "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==", "dev": true, "license": "MIT"}, "node_modules/tabster": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/tabster/-/tabster-8.5.6.tgz", "integrity": "sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==", "license": "MIT", "dependencies": {"keyborg": "2.6.0", "tslib": "^2.8.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.40.0"}}, "node_modules/tabster/node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.40.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.0.tgz", "integrity": "sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/terser": {"version": "5.24.0", "resolved": "https://registry.npmjs.org/terser/-/terser-5.24.0.tgz", "integrity": "sha512-ZpGR4Hy3+wBEzVEnHvstMvqpD/nABNelQn/z2r0fjVWGQsN3bpOLzQlqDxmb4CDZnXq5lpjnQ+mHQLAOpfM5iw==", "dev": true, "optional": true, "peer": true, "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true, "optional": true, "peer": true}, "node_modules/tinybench": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz", "integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==", "dev": true, "license": "MIT"}, "node_modules/tinyexec": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.2.tgz", "integrity": "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==", "dev": true, "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinypool": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/tinypool/-/tinypool-1.1.1.tgz", "integrity": "sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}}, "node_modules/tinyrainbow": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-2.0.0.tgz", "integrity": "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.3.tgz", "integrity": "sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tldts": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts/-/tldts-6.1.86.tgz", "integrity": "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ==", "dev": true, "license": "MIT", "dependencies": {"tldts-core": "^6.1.86"}, "bin": {"tldts": "bin/cli.js"}}, "node_modules/tldts-core": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.86.tgz", "integrity": "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA==", "dev": true, "license": "MIT"}, "node_modules/tough-cookie": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tldts": "^6.1.32"}, "engines": {"node": ">=16"}}, "node_modules/tr46": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz", "integrity": "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw==", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "dev": true, "license": "MIT"}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/use-disposable": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/use-disposable/-/use-disposable-1.0.4.tgz", "integrity": "sha512-j83t6AMLWUyb5zwlTDqf6dP9LezM9R0yTbI/b6olmdaGtCKQUe9pgJWV6dRaaQLcozypjIEp4EmZr2DkZGKLSg==", "license": "MIT", "peerDependencies": {"@types/react": ">=16.8.0 <19.0.0", "@types/react-dom": ">=16.8.0 <19.0.0", "react": ">=16.8.0 <19.0.0", "react-dom": ">=16.8.0 <19.0.0"}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/vite": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/vite/-/vite-7.0.6.tgz", "integrity": "sha512-MHFiOENNBd+Bd9uvc8GEsIzdkn1JxMmEeYX35tI3fv0sJBUTfW5tQsoaOwuY4KhBI09A3dUJ/DXf2yxPVPUceg==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.6", "picomatch": "^4.0.3", "postcss": "^8.5.6", "rollup": "^4.40.0", "tinyglobby": "^0.2.14"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^20.19.0 || >=22.12.0", "jiti": ">=1.21.0", "less": "^4.0.0", "lightningcss": "^1.21.0", "sass": "^1.70.0", "sass-embedded": "^1.70.0", "stylus": ">=0.54.8", "sugarss": "^5.0.0", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-node": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vite-node/-/vite-node-3.2.4.tgz", "integrity": "sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.4.1", "es-module-lexer": "^1.7.0", "pathe": "^2.0.3", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vite-tsconfig-paths": {"version": "5.1.4", "resolved": "https://registry.npmjs.org/vite-tsconfig-paths/-/vite-tsconfig-paths-5.1.4.tgz", "integrity": "sha512-cYj0LRuLV2c2sMqhqhGpaO3LretdtMn/BVX4cPLanIZuwwrkVl+lK84E/miEXkCHWXuq65rhNN4rXsBcOB3S4w==", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.1", "globrex": "^0.1.2", "tsconfck": "^3.0.3"}, "peerDependencies": {"vite": "*"}, "peerDependenciesMeta": {"vite": {"optional": true}}}, "node_modules/vite-tsconfig-paths/node_modules/tsconfck": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/tsconfck/-/tsconfck-3.1.1.tgz", "integrity": "sha512-00eoI6WY57SvZEVjm13stEVE90VkEdJAFGgpFLTsZbJyW/LwFQ7uQxJHWpZ2hzSWgCPKc9AnBnNP+0X7o3hAmQ==", "dev": true, "bin": {"tsconfck": "bin/tsconfck.js"}, "engines": {"node": "^18 || >=20"}, "peerDependencies": {"typescript": "^5.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vitest": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vitest/-/vitest-3.2.4.tgz", "integrity": "sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/expect": "3.2.4", "@vitest/mocker": "3.2.4", "@vitest/pretty-format": "^3.2.4", "@vitest/runner": "3.2.4", "@vitest/snapshot": "3.2.4", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "debug": "^4.4.1", "expect-type": "^1.2.1", "magic-string": "^0.30.17", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tinypool": "^1.1.1", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0", "vite-node": "3.2.4", "why-is-node-running": "^2.3.0"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/debug": "^4.1.12", "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "@vitest/browser": "3.2.4", "@vitest/ui": "3.2.4", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/debug": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/w3c-xmlserializer": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-5.0.0.tgz", "integrity": "sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/web-vitals": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/web-vitals/-/web-vitals-5.0.3.tgz", "integrity": "sha512-4KmOFYxj7qT6RAdCH0SWwq8eKeXNhAFXR4PmgF6nrWFmrJ41n7lq3UCA6UK0GebQ4uu+XP8e8zGjaDO3wZlqTg==", "license": "Apache-2.0"}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/whatwg-encoding": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz", "integrity": "sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=18"}}, "node_modules/whatwg-mimetype": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz", "integrity": "sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/whatwg-url": {"version": "14.2.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz", "integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "dev": true, "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/why-is-node-running": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz", "integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/ws": {"version": "8.18.2", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-5.0.0.tgz", "integrity": "sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18"}}, "node_modules/xmlchars": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz", "integrity": "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==", "dev": true, "license": "MIT"}, "node_modules/yaml": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz", "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==", "dev": true, "license": "ISC", "optional": true, "peer": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}}, "dependencies": {"@ampproject/remapping": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "dev": true, "requires": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@asamuzakjp/css-color": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.2.0.tgz", "integrity": "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==", "dev": true, "requires": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}, "dependencies": {"lru-cache": {"version": "10.4.3", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "dev": true}}}, "@babel/code-frame": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}}, "@babel/compat-data": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==", "dev": true}, "@babel/core": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz", "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==", "dev": true, "requires": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "dependencies": {"convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}}}, "@babel/generator": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz", "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==", "dev": true, "requires": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "dependencies": {"@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "dev": true, "requires": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}}}, "@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "dev": true, "requires": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "dependencies": {"lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "requires": {"yallist": "^3.0.2"}}, "semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true}, "yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true}}}, "@babel/helper-globals": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "dev": true}, "@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "dev": true, "requires": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}}, "@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "dev": true, "requires": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}}, "@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "dev": true}, "@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true}, "@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "dev": true}, "@babel/helpers": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "dev": true, "requires": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}}, "@babel/parser": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "dev": true, "requires": {"@babel/types": "^7.28.0"}}, "@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.27.1"}}, "@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q=="}, "@babel/template": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "dev": true, "requires": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}}, "@babel/traverse": {"version": "7.28.0", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==", "dev": true, "requires": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}}, "@babel/types": {"version": "7.28.1", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz", "integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}}, "@csstools/color-helpers": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.2.tgz", "integrity": "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA==", "dev": true}, "@csstools/css-calc": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.4.tgz", "integrity": "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==", "dev": true, "requires": {}}, "@csstools/css-color-parser": {"version": "3.0.10", "resolved": "https://registry.npmjs.org/@csstools/css-color-parser/-/css-color-parser-3.0.10.tgz", "integrity": "sha512-TiJ5Ajr6WRd1r8HSiwJvZBiJOqtH86aHpUjq5aEKWHiII2Qfjqd/HCWKPOW8EP4vcspXbHnXrwIDlu5savQipg==", "dev": true, "requires": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}}, "@csstools/css-parser-algorithms": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.5.tgz", "integrity": "sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ==", "dev": true, "requires": {}}, "@csstools/css-tokenizer": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz", "integrity": "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw==", "dev": true}, "@ctrl/tinycolor": {"version": "3.6.1", "resolved": "https://registry.npmjs.org/@ctrl/tinycolor/-/tinycolor-3.6.1.tgz", "integrity": "sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA=="}, "@emotion/hash": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g=="}, "@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==", "dev": true, "optional": true}, "@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==", "dev": true, "optional": true}, "@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==", "dev": true, "optional": true}, "@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==", "dev": true, "optional": true}, "@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz", "integrity": "sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==", "dev": true, "optional": true}, "@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==", "dev": true, "optional": true}, "@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==", "dev": true, "optional": true}, "@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==", "dev": true, "optional": true}, "@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==", "dev": true, "optional": true}, "@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==", "dev": true, "optional": true}, "@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==", "dev": true, "optional": true}, "@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==", "dev": true, "optional": true}, "@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==", "dev": true, "optional": true}, "@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==", "dev": true, "optional": true}, "@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==", "dev": true, "optional": true}, "@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==", "dev": true, "optional": true}, "@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==", "dev": true, "optional": true}, "@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==", "dev": true, "optional": true}, "@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==", "dev": true, "optional": true}, "@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "dev": true, "optional": true}, "@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==", "dev": true, "optional": true}, "@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==", "dev": true, "optional": true}, "@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==", "dev": true, "optional": true}, "@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==", "dev": true, "optional": true}, "@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==", "dev": true, "optional": true}, "@floating-ui/core": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz", "integrity": "sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==", "requires": {"@floating-ui/utils": "^0.2.10"}}, "@floating-ui/devtools": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/@floating-ui/devtools/-/devtools-0.2.1.tgz", "integrity": "sha512-8PHJLbD6VhBh+LJ1uty/Bz30qs02NXCE5u8WpOhSewlYXUWl03GNXknr9AS2yaAWJEQaY27x7eByJs44gODBcw==", "requires": {}}, "@floating-ui/dom": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz", "integrity": "sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==", "requires": {"@floating-ui/core": "^1.7.2", "@floating-ui/utils": "^0.2.10"}}, "@floating-ui/utils": {"version": "0.2.10", "resolved": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz", "integrity": "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ=="}, "@fluentui/keyboard-keys": {"version": "9.0.8", "resolved": "https://registry.npmjs.org/@fluentui/keyboard-keys/-/keyboard-keys-9.0.8.tgz", "integrity": "sha512-iUSJUUHAyTosnXK8O2Ilbfxma+ZyZPMua5vB028Ys96z80v+LFwntoehlFsdH3rMuPsA8GaC1RE7LMezwPBPdw==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/priority-overflow": {"version": "9.1.15", "resolved": "https://registry.npmjs.org/@fluentui/priority-overflow/-/priority-overflow-9.1.15.tgz", "integrity": "sha512-/3jPBBq64hRdA416grVj+ZeMBUIaKZk2S5HiRg7CKCAV1JuyF84Do0rQI6ns8Vb9XOGuc4kurMcL/UEftoEVrg==", "requires": {"@swc/helpers": "^0.5.1"}}, "@fluentui/react-accordion": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-accordion/-/react-accordion-9.8.0.tgz", "integrity": "sha512-YHvTZCdARlDKF69qt3nQc+Q4N3uFTUDmZGg97/H+HZAbpTlwnQVsw//y860M828d5SMyvutNm6BGqpsb+XBudw==", "requires": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-alert": {"version": "9.0.0-beta.124", "resolved": "https://registry.npmjs.org/@fluentui/react-alert/-/react-alert-9.0.0-beta.124.tgz", "integrity": "sha512-yFBo3B5H9hnoaXxlkuz8wRz04DEyQ+ElYA/p5p+Vojf19Zuta8DmFZZ6JtWdtxcdnnQ4LvAfC5OYYlzdReozPA==", "requires": {"@fluentui/react-avatar": "^9.6.29", "@fluentui/react-button": "^9.3.83", "@fluentui/react-icons": "^2.0.239", "@fluentui/react-jsx-runtime": "^9.0.39", "@fluentui/react-tabster": "^9.21.5", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.10", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-aria": {"version": "9.15.4", "resolved": "https://registry.npmjs.org/@fluentui/react-aria/-/react-aria-9.15.4.tgz", "integrity": "sha512-5t/BrCQOWz3ZAbCy6RHN3iT3+MiwbHe3ESZXoxSquxVJzBjDixuvzhnls83cqC86OaWi2fp2kI8e3/BvLA54+Q==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-avatar": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-avatar/-/react-avatar-9.9.0.tgz", "integrity": "sha512-2KWRkz7khP42ROD/thdID+dHhyCz8irQp37pD3pyLRAZe7Su1ckkjbaSB3aBl3ee0rmVq8vQmyulshsGZkyFJg==", "requires": {"@fluentui/react-badge": "^9.4.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-badge": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-badge/-/react-badge-9.4.0.tgz", "integrity": "sha512-FS12bACA0i5YFwTjYT1aF0NBSoNgPdZTNXM/MqJpqOq6UyCylRf75ro06a0LduU671gB578Ap+yzk8E3+Ia9NQ==", "requires": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-breadcrumb": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-breadcrumb/-/react-breadcrumb-9.3.0.tgz", "integrity": "sha512-t8EAbhyO/wFJAzEr921Oag0yrkKcX6zprqzJ1dybWv8ndyjbJdQcut0fkOeMwmXCgu3MoBirW27s+4gHODwidw==", "requires": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-link": "^9.6.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-button": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-button/-/react-button-9.6.0.tgz", "integrity": "sha512-rsSGqJrXs4NL8Lo/2BCDEGYZrGj3Kkg2crVYnG3xBC2GMFGmlww+ovsIUcWhMo6KRY87F8dyqUcqX1g+HJNv/A==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-card": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-card/-/react-card-9.4.0.tgz", "integrity": "sha512-hH862zMzVVS1BRE2UGH8ZrLT0z1yLg4LRn4L8onEfCAKj5E65o+trGH4T6c0TOLexNyJKeF6bqrQDUtbT35pIA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-text": "^9.6.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-carousel": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-carousel/-/react-carousel-9.8.0.tgz", "integrity": "sha512-6BRHOSzaY7gkSvktaHBfa3FE/Tdmjel0o1lrR0Zl1D0kdbUDtY8ICb0FtROJ4YLSE2YyLWmAMlR3MbxKWPmCcw==", "requires": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-fade": "^8.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-checkbox": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-checkbox/-/react-checkbox-9.5.0.tgz", "integrity": "sha512-HB4zac4C0Msqbrjl7AOTuEMnmpEyKeNTaKc8eb9MDU8xJVWzWS5Q91TWpmXOXgneaG3/pu5ops749zBmlCU1Pg==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-color-picker": {"version": "9.2.0", "resolved": "https://registry.npmjs.org/@fluentui/react-color-picker/-/react-color-picker-9.2.0.tgz", "integrity": "sha512-4E6woOMxj4Tyy0sHAORR8pGUlZbtoGgQ6UsdQ38SWEU+f/zo/2SsyJOqtuMur67+ThAoJR5bq+W3mLGi8WhAPA==", "requires": {"@ctrl/tinycolor": "^3.3.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-combobox": {"version": "9.16.0", "resolved": "https://registry.npmjs.org/@fluentui/react-combobox/-/react-combobox-9.16.0.tgz", "integrity": "sha512-w84o5ubLL4MCfbzb/xCRoWjc1S2ZGk0Ci8PEXkP+CFAl3SxAORJISAiMCbfk+ZoWAwNLFcHNO6UFj2XH+fWkbA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-components": {"version": "9.67.0", "resolved": "https://registry.npmjs.org/@fluentui/react-components/-/react-components-9.67.0.tgz", "integrity": "sha512-692/t+6te3HO0/tA5585CrA9FA6AIR7a6UVJ/p6Cah0cwRfi/ffeNYZ5fhZHX/46DU0SRfAXemcsoFE1cgKpYA==", "requires": {"@fluentui/react-accordion": "^9.8.0", "@fluentui/react-alert": "9.0.0-beta.124", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-badge": "^9.4.0", "@fluentui/react-breadcrumb": "^9.3.0", "@fluentui/react-button": "^9.6.0", "@fluentui/react-card": "^9.4.0", "@fluentui/react-carousel": "^9.8.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-color-picker": "^9.2.0", "@fluentui/react-combobox": "^9.16.0", "@fluentui/react-dialog": "^9.14.0", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-drawer": "^9.9.0", "@fluentui/react-field": "^9.4.0", "@fluentui/react-image": "^9.3.0", "@fluentui/react-infobutton": "9.0.0-beta.102", "@fluentui/react-infolabel": "^9.4.0", "@fluentui/react-input": "^9.7.0", "@fluentui/react-label": "^9.3.0", "@fluentui/react-link": "^9.6.0", "@fluentui/react-list": "^9.3.0", "@fluentui/react-menu": "^9.19.0", "@fluentui/react-message-bar": "^9.6.0", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-nav": "^9.3.0", "@fluentui/react-overflow": "^9.5.0", "@fluentui/react-persona": "^9.5.0", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-progress": "^9.4.0", "@fluentui/react-provider": "^9.22.0", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-rating": "^9.3.0", "@fluentui/react-search": "^9.3.0", "@fluentui/react-select": "^9.4.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-skeleton": "^9.4.0", "@fluentui/react-slider": "^9.5.0", "@fluentui/react-spinbutton": "^9.5.0", "@fluentui/react-spinner": "^9.7.0", "@fluentui/react-swatch-picker": "^9.4.0", "@fluentui/react-switch": "^9.4.0", "@fluentui/react-table": "^9.18.0", "@fluentui/react-tabs": "^9.9.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-tag-picker": "^9.7.0", "@fluentui/react-tags": "^9.7.0", "@fluentui/react-teaching-popover": "^9.6.0", "@fluentui/react-text": "^9.6.0", "@fluentui/react-textarea": "^9.6.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-toast": "^9.6.0", "@fluentui/react-toolbar": "^9.6.0", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-tree": "^9.12.0", "@fluentui/react-utilities": "^9.22.0", "@fluentui/react-virtualizer": "9.0.0-alpha.100", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-dialog": {"version": "9.14.0", "resolved": "https://registry.npmjs.org/@fluentui/react-dialog/-/react-dialog-9.14.0.tgz", "integrity": "sha512-FgvxWVwET9niVhWoD1gpEx7MICOCDncTyreJV12KmCVC0eYxvun0uQmA6FXVnh+3yh/9AhIH0KfiKa0C8qsP7g==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-divider": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-divider/-/react-divider-9.4.0.tgz", "integrity": "sha512-WLs/12FP7Yf+SYCISzxGaNbLvJjZyBcUFbG9KhhRmt5CcwIklTinEJWW3qXcAmS+nHuGdkwpgC/avgEjzpYMcg==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-drawer": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-drawer/-/react-drawer-9.9.0.tgz", "integrity": "sha512-HjW13Tikmk7s/XUKGoYn6MKsvm9gmO6Og8h3PtcWIccsXBUesQtWAgNJpgvprEDKHFwLF5aB1fHqYDsStbrLCw==", "requires": {"@fluentui/react-dialog": "^9.14.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-field": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-field/-/react-field-9.4.0.tgz", "integrity": "sha512-X4XWe1gWVxUP6Oa395Ekpdtj9FX2WAWPj5+DGW8OGB7SNJA67cEP/E8FCEA/tflm0eZXaHVFThh0yElf1KX7nw==", "requires": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-icons": {"version": "2.0.304", "resolved": "https://registry.npmjs.org/@fluentui/react-icons/-/react-icons-2.0.304.tgz", "integrity": "sha512-8Pooy3e7NZUBcvBEpJUy1xmapS1ouT9s42rcNgOvou4wmcv10n+iFtsUUHswJ9Zr8F/gn1V0YcA2SqtFbu7P8Q==", "requires": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}}, "@fluentui/react-image": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-image/-/react-image-9.3.0.tgz", "integrity": "sha512-qhKZ6Dj267UPvnAwzmvLD3JDb8zSCEtkL2c9CLyUAcuuvT4KubhNsLudY//1EMiC5a+Du0gC2lcxRT84PQ2NZg==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infobutton": {"version": "9.0.0-beta.102", "resolved": "https://registry.npmjs.org/@fluentui/react-infobutton/-/react-infobutton-9.0.0-beta.102.tgz", "integrity": "sha512-3kA4F0Vga8Ds6JGlBajLCCDOo/LmPuS786Wg7ui4ZTDYVIMzy1yp2XuVcZniifBFvEp0HQCUoDPWUV0VI3FfzQ==", "requires": {"@fluentui/react-icons": "^2.0.237", "@fluentui/react-jsx-runtime": "^9.0.36", "@fluentui/react-label": "^9.1.68", "@fluentui/react-popover": "^9.9.6", "@fluentui/react-tabster": "^9.21.0", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-utilities": "^9.18.7", "@griffel/react": "^1.5.14", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-infolabel": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-infolabel/-/react-infolabel-9.4.0.tgz", "integrity": "sha512-ABSzkV/FN0TfKRXbarb+/dWihgKpqDeS5YWf69pCeXg7s+Ls3UQn/7+mgBjHcMOoRpbqW45bOzCoC+6Iqb2ggg==", "requires": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-input": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-input/-/react-input-9.7.0.tgz", "integrity": "sha512-rJCVaVnAidVtp//DQFaz1vHMbiNVcxZPjvZ9xfIpdRjFk+kSEkcRj1AT/iCMqwTXhJb9hYIMJRE+gPQoTiQYdQ==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-jsx-runtime": {"version": "9.1.2", "resolved": "https://registry.npmjs.org/@fluentui/react-jsx-runtime/-/react-jsx-runtime-9.1.2.tgz", "integrity": "sha512-igGuh0P7Gd09Kk3g6JwjnaRIRk+mluCbpf+KcAUde6bxZ/5qB50HGX+DOGWa3+RPd5240+HLBxpT3Y985INgqw==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1", "react-is": "^17.0.2"}}, "@fluentui/react-label": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-label/-/react-label-9.3.0.tgz", "integrity": "sha512-H<PERSON>i4TBEjkJoeNZ9FOL8VPnOwrKrJp5drd1f00cICwRz7cimSZt56C97BwM9IB41nEdF3Yk3MLd4Hea1PO+Msg==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-link": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-link/-/react-link-9.6.0.tgz", "integrity": "sha512-2G+IWuT98pt1HwJWuL9VuTQesUdYjDooK/LPUOsXaVwwGP71lKBXQ6B7ZBw5bqDt3dwborTugyG6RlD7aDpPbw==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-list": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-list/-/react-list-9.3.0.tgz", "integrity": "sha512-OsYz2ULKXnFEExZW8FaUk1+cjPcFIrtRlwytKDAnRvwyBLIhhQezRWWTEVpc2M75NmZbkZtqyDujdB/ZdSlOmA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-menu": {"version": "9.19.0", "resolved": "https://registry.npmjs.org/@fluentui/react-menu/-/react-menu-9.19.0.tgz", "integrity": "sha512-Wy/8DaHXEtntJk2onVWZI19AHIJkAJB9gXjXrKFk4DbSX0n9Brj06dBu9lZzl5q4i7cUQhg9sMayle3ovspX6w==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-message-bar": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-message-bar/-/react-message-bar-9.6.0.tgz", "integrity": "sha512-sGVd+wK2NsiHBcGl1Pw3P4LJW50hbaN4+4NA5udCwbtIW97lO2zMFJtROU+oBYkmV0HbJ9jSxOYyeMmndjKjAQ==", "requires": {"@fluentui/react-button": "^9.6.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-link": "^9.6.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "react-transition-group": "^4.4.1"}}, "@fluentui/react-motion": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-motion/-/react-motion-9.9.0.tgz", "integrity": "sha512-xgm/CkU1UvemooplEFKJL9mfGJFvzId2DJ1WYTFAa5TSZMtzOAZuPuwS/PrPNFuwjnhvCMShDj8zazgvR5i37A==", "requires": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-motion-components-preview": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-motion-components-preview/-/react-motion-components-preview-0.7.0.tgz", "integrity": "sha512-vGxi2KLqwCzfV2WSZBYGKSzKnfsnGKjkQpE5qYfwk0aPp3iDXtyiLCANgNiPKIBJ4R+/48SAbIDKaiXBtd7GRw==", "requires": {"@fluentui/react-motion": "*", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-nav": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-nav/-/react-nav-9.3.0.tgz", "integrity": "sha512-IodGcAPlH45pNskmPmFsXF8IGGrRAEcd4PrytdAPFhBx0Ov69uvoI1B7mCTDGYYb0g8KRW751rGJtU4QMgUAUw==", "requires": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-drawer": "^9.9.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-tooltip": "^9.8.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-overflow": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-overflow/-/react-overflow-9.5.0.tgz", "integrity": "sha512-XIJ2WGNiSs4KER5GIV9iMQA/lGVSR2eE+Aeht+hGiwlmn/YvTvS5SM/LSw2CKyi1LkVRzNB3Kj1wiIzD/he5+Q==", "requires": {"@fluentui/priority-overflow": "^9.1.15", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-persona": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-persona/-/react-persona-9.5.0.tgz", "integrity": "sha512-0MnNTqrJ3BxTXvg+NdLE9mabSmLFVKiuqdIAtK/gYFiEk43wGskMUx9Kw1Dfq6xRYQImaFnoLhd+47YsLyn9jg==", "requires": {"@fluentui/react-avatar": "^9.9.0", "@fluentui/react-badge": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-popover": {"version": "9.12.0", "resolved": "https://registry.npmjs.org/@fluentui/react-popover/-/react-popover-9.12.0.tgz", "integrity": "sha512-qnPwYW3E63jLTaVB7ssbTVE9ez04eNmky7SjdD2MlU6F2506nuV5V7wPp3Z5LZpD6SQqgMjtPiTlcFgWHAjAvw==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-portal": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-portal/-/react-portal-9.7.0.tgz", "integrity": "sha512-g9Q9wsw4OH4UFYyjb5BfbL7GwaloIiFMVZXie9q0lLeo9JUFhNHh/2X7UUGesagCO86WMGN1haQUA7uaN6gIXA==", "requires": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-disposable": "^1.0.1"}}, "@fluentui/react-positioning": {"version": "9.20.0", "resolved": "https://registry.npmjs.org/@fluentui/react-positioning/-/react-positioning-9.20.0.tgz", "integrity": "sha512-qbxIYG8N+zBVXsgyiqd8kQzDiEn+eabnDBn3hqhaolVqn3QVWfgjoARJXYuKbUY0GDMPMukW1PH2NbEl5BvQXQ==", "requires": {"@floating-ui/devtools": "0.2.1", "@floating-ui/dom": "^1.6.12", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}}, "@fluentui/react-progress": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-progress/-/react-progress-9.4.0.tgz", "integrity": "sha512-EplT3K95DPob22MV0mIzLmbzsdS2bhMPEiRjUAsRpUPnw5gRJi4OKneS5y3mRCBUiFjlkzEDwTTbEa+NkZEXlg==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-provider": {"version": "9.22.0", "resolved": "https://registry.npmjs.org/@fluentui/react-provider/-/react-provider-9.22.0.tgz", "integrity": "sha512-dyrux/z+OXTM9U0uaq/AHtSI/5jZsehw3LND79StMP11ebi9lGjyRthZ3M8E6Pq7LlSgQ0yVnMFYZc9WoijVHg==", "requires": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/core": "^1.16.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-radio": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-radio/-/react-radio-9.5.0.tgz", "integrity": "sha512-9j4t85KdIYu5TN3tN1S2KlIfzL4FNYRuFBsQ8WxB0F8vmGlyIxUt9S2dRG3+MScqOwIS2Q0HAmZhu0hrTJVWRg==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-rating": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-rating/-/react-rating-9.3.0.tgz", "integrity": "sha512-FP19VCBG3aQm7uP/pORfDBKHU/f5YinvETe39y4+9VPiXlgbF+sqjwXGB6N7kvu9ZdTD4ZFrMW4FaSLYrpJEtA==", "requires": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-search": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/@fluentui/react-search/-/react-search-9.3.0.tgz", "integrity": "sha512-RMzYhNdrLpz5/e6Z3NlDmX7KP+AXz0N0e4SBoKjHauoDfEPD9+oHbSbah/JQWmw290h1jUUrElRwPYoIQ8eSgg==", "requires": {"@fluentui/react-icons": "^2.0.245", "@fluentui/react-input": "^9.7.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-select": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-select/-/react-select-9.4.0.tgz", "integrity": "sha512-6DoC6Xc6hkHKCzRFjB2UYbJAa3v+KZ/OUML18OvYvdGkEtv+n2x3sc+mUDgFuXHqB/4OIhUDXq4S/Mriwd8KUg==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-shared-contexts": {"version": "9.24.0", "resolved": "https://registry.npmjs.org/@fluentui/react-shared-contexts/-/react-shared-contexts-9.24.0.tgz", "integrity": "sha512-GA+uLv711E+YGrAP/aVB15ozvNCiuB2ZrPDC9aYF+A6sRDxoZZG8VgHjhQ/YWJfVjDXLky4ihirknzsW1sjGtg==", "requires": {"@fluentui/react-theme": "^9.1.24", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-skeleton": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-skeleton/-/react-skeleton-9.4.0.tgz", "integrity": "sha512-n6viQkyI+g7ljf33x/6FVwNfyfJq6Qosug5OlxsSTrneyn+kSb6lw8K4z3AUIEBOR65XEonYWegXOm4ldcJYOw==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-slider": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-slider/-/react-slider-9.5.0.tgz", "integrity": "sha512-qxLRYBKKEbRuKdHzE0iSpETvjYKGjIK4Rm18swFd5Jl4SfXUxaq6EuHRE1sfiOhraH2nDSKHVT+iXZxYi/g+Tg==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinbutton": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/@fluentui/react-spinbutton/-/react-spinbutton-9.5.0.tgz", "integrity": "sha512-rRdgwNb0yNJOeCwbr6Kn1VX+ys+4PEfl6bwHphXy/6iwbF7BETtZjmGGbfXhuu+WsLxQxHnyeo5uC21E/mbWqg==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-spinner": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-spinner/-/react-spinner-9.7.0.tgz", "integrity": "sha512-B9KQ6Muy2KZIBpmzkdZ0ONu4Ao/3iMhBous1Emq7wfiYEhoz1pOLKvVgh+IgXz5SX28x8cZiDt9/Hu7Quf6zJg==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-swatch-picker": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-swatch-picker/-/react-swatch-picker-9.4.0.tgz", "integrity": "sha512-KSeIvU/fwBeXP5irqQxSvs34LNu03a3NYF48GOJrDODUwv/tjYn+/IgsPRMjA2pZ502AMWFa5OSKpeUJ9mbi1g==", "requires": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-switch": {"version": "9.4.0", "resolved": "https://registry.npmjs.org/@fluentui/react-switch/-/react-switch-9.4.0.tgz", "integrity": "sha512-8uKP2aM/doLGprYuljbJAbAapeVWbgMW1FLQH53+RHURZNy1Gvt8AiisllJwtmQC8esgK4xlbaSzn/b1/S8B8A==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-label": "^9.3.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-table": {"version": "9.18.0", "resolved": "https://registry.npmjs.org/@fluentui/react-table/-/react-table-9.18.0.tgz", "integrity": "sha512-yBdBvY5X/XnX5WYoFseKlqc0pYomBZ+3jFaMEeWMYxOAIuHWif3IUq4kTxBoweKcFMmclMNMpY22j/6YcFwHXA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-tabs": {"version": "9.9.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tabs/-/react-tabs-9.9.0.tgz", "integrity": "sha512-V06heimvtH5LcjjePkl8ETWrX4YN1V2STQhFr6lXn6FjS8nsNGhWemHduCi2qY3DLyZgYLBGrOR5AgSbbv5jcA==", "requires": {"@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-tabster": {"version": "9.26.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tabster/-/react-tabster-9.26.0.tgz", "integrity": "sha512-ENaISUye53JLvAN3VqiKzTdDSubnMucG/mcGuB+QbnzTLGIHxvEYq/GV4WHwWbQwjZPXAG9Hr0F0l0AFzrkeFA==", "requires": {"@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "keyborg": "^2.6.0", "tabster": "^8.5.5"}}, "@fluentui/react-tag-picker": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tag-picker/-/react-tag-picker-9.7.0.tgz", "integrity": "sha512-p0xAxemN/fYlDG6dVbkcGybjMCiNravyzTsnpE2OwEoh3eDfsL5oXipPkcJACzv5ZhmavVyAHs4txenEcW24gw==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-combobox": "^9.16.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-field": "^9.4.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-tags": "^9.7.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-tags": {"version": "9.7.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tags/-/react-tags-9.7.0.tgz", "integrity": "sha512-TU7CPouGFuOXxGVjrbWbLgyTNrVoyxOS3xvwdZGJuGlaU9FbFuzKNUeV/CL0o6SiA/0O1wGa4/VV6XRuUGQX3Q==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-teaching-popover": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-teaching-popover/-/react-teaching-popover-9.6.0.tgz", "integrity": "sha512-/JX1+W/ff8bkO1nCSExL9ASP1zfysUInc83V/6XzRgwhyNMkUoNgGRw32EDpxz6Ympe8mZnQKWNUmvTsxr28aQ==", "requires": {"@fluentui/react-aria": "^9.15.4", "@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-popover": "^9.12.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1", "use-sync-external-store": "^1.2.0"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-text": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-text/-/react-text-9.6.0.tgz", "integrity": "sha512-/ZAMjgAn5sgbAjYnwmM5k0kxgNehpccxXI6f5uJ72IfAmj85dMH4TDNsN6xOCIMhj+xDxuBIT4axEYt+wAoF1Q==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-textarea": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-textarea/-/react-textarea-9.6.0.tgz", "integrity": "sha512-o6jAAB4cIzzPLBj8/RDo+my7yXSQtFCC+O2p4mD2X+hUvBCydoQI+45RbEeJXXwEsWjUp7XfbLUyt3mB8dH0xQ==", "requires": {"@fluentui/react-field": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-theme": {"version": "9.1.24", "resolved": "https://registry.npmjs.org/@fluentui/react-theme/-/react-theme-9.1.24.tgz", "integrity": "sha512-OhVKYD7CMYHxzJEn4PtIszledj8hbQJNWBMfIZsp4Sytdp9vCi0txIQUx4BhS1WqtQPhNGCF16eW9Q3NRrnIrQ==", "requires": {"@fluentui/tokens": "1.0.0-alpha.21", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-toast": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-toast/-/react-toast-9.6.0.tgz", "integrity": "sha512-t/eUl3w8RdLFMLHcvWHXCH9jec29MV7K7pqmyXsW2g7edaChTyCbkxlII861IvY+XqwIvNlpczzh4cgkyzAj/w==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-toolbar": {"version": "9.6.0", "resolved": "https://registry.npmjs.org/@fluentui/react-toolbar/-/react-toolbar-9.6.0.tgz", "integrity": "sha512-tbpM8prz8cDTzeF7PudjTA3UQruVrjGNSsTwR+vmIGVo0E986Zz+VSJaLICeC2ttiHOirhqm6goswP+bGG5Evw==", "requires": {"@fluentui/react-button": "^9.6.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-divider": "^9.4.0", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-tooltip": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tooltip/-/react-tooltip-9.8.0.tgz", "integrity": "sha512-LW0ouXkPXxx+XPScLB9tWlqn11cVHxmDJ3weZXuWrl5jjx4agjqKHGC8MOdr4Un+2hoO0g2BcrlDaQNhsMPgYA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-portal": "^9.7.0", "@fluentui/react-positioning": "^9.20.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-tree": {"version": "9.12.0", "resolved": "https://registry.npmjs.org/@fluentui/react-tree/-/react-tree-9.12.0.tgz", "integrity": "sha512-vehLCk918YN53h8sGs4jx5oEF2twdVRdoIQ+csuLUkxRhl7f6eWyQWRk/R2lZlJgsz0vgk07RB/Yfx8/BFEUiA==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-aria": "^9.15.4", "@fluentui/react-avatar": "^9.9.0", "@fluentui/react-button": "^9.6.0", "@fluentui/react-checkbox": "^9.5.0", "@fluentui/react-context-selector": "^9.2.2", "@fluentui/react-icons": "^2.0.245", "@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-motion": "^9.9.0", "@fluentui/react-motion-components-preview": "^0.7.0", "@fluentui/react-radio": "^9.5.0", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-tabster": "^9.26.0", "@fluentui/react-theme": "^9.1.24", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}, "dependencies": {"@fluentui/react-context-selector": {"version": "9.2.2", "resolved": "https://registry.npmjs.org/@fluentui/react-context-selector/-/react-context-selector-9.2.2.tgz", "integrity": "sha512-R9710dBH2AYNbdQz0UpvSqoA8YZ8vVicyqGvWPKvDGCNbZB6GY1Cu5LbODpeAthylLXhgXxIlGEcoOpjBBpRbA==", "requires": {"@fluentui/react-utilities": "^9.22.0", "@swc/helpers": "^0.5.1"}}, "scheduler": {"version": "0.23.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "integrity": "sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==", "peer": true, "requires": {"loose-envify": "^1.1.0"}}}}, "@fluentui/react-utilities": {"version": "9.22.0", "resolved": "https://registry.npmjs.org/@fluentui/react-utilities/-/react-utilities-9.22.0.tgz", "integrity": "sha512-O4D51FUyn5670SjduzzN1usmwWAmFPQA00Gu6jJrbDXvOXTpOAO/ApkLpSW87HChKGrj8Y0gjFHtK8xpC3qOCg==", "requires": {"@fluentui/keyboard-keys": "^9.0.8", "@fluentui/react-shared-contexts": "^9.24.0", "@swc/helpers": "^0.5.1"}}, "@fluentui/react-virtualizer": {"version": "9.0.0-alpha.100", "resolved": "https://registry.npmjs.org/@fluentui/react-virtualizer/-/react-virtualizer-9.0.0-alpha.100.tgz", "integrity": "sha512-e7u3SP2Smv5+9Adey+pOerGmHq2D6Nd0ek/iWbc/o0CKX5QMeHwbUlZAbVVsrX/vwIeeZ3+qJMt+UH3hHI+wdw==", "requires": {"@fluentui/react-jsx-runtime": "^9.1.2", "@fluentui/react-shared-contexts": "^9.24.0", "@fluentui/react-utilities": "^9.22.0", "@griffel/react": "^1.5.22", "@swc/helpers": "^0.5.1"}}, "@fluentui/tokens": {"version": "1.0.0-alpha.21", "resolved": "https://registry.npmjs.org/@fluentui/tokens/-/tokens-1.0.0-alpha.21.tgz", "integrity": "sha512-xQ1T56sNgDFGl+kJdIwhz67mHng8vcwO7Dvx5Uja4t+NRULQBgMcJ4reUo4FGF3TjufHj08pP0/OnKQgnOaSVg==", "requires": {"@swc/helpers": "^0.5.1"}}, "@griffel/core": {"version": "1.19.2", "resolved": "https://registry.npmjs.org/@griffel/core/-/core-1.19.2.tgz", "integrity": "sha512-WkB/QQkjy9dE4vrNYGhQvRRUHFkYVOuaznVOMNTDT4pS9aTJ9XPrMTXXlkpcwaf0D3vNKoerj4zAwnU2lBzbOg==", "requires": {"@emotion/hash": "^0.9.0", "@griffel/style-types": "^1.3.0", "csstype": "^3.1.3", "rtl-css-js": "^1.16.1", "stylis": "^4.2.0", "tslib": "^2.1.0"}}, "@griffel/react": {"version": "1.5.30", "resolved": "https://registry.npmjs.org/@griffel/react/-/react-1.5.30.tgz", "integrity": "sha512-1q4ojbEVFY5YA0j1NamP0WWF4BKh+GHsVugltDYeEgEaVbH3odJ7tJabuhQgY+7Nhka0pyEFWSiHJev0K3FSew==", "requires": {"@griffel/core": "^1.19.2", "tslib": "^2.1.0"}}, "@griffel/style-types": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@griffel/style-types/-/style-types-1.3.0.tgz", "integrity": "sha512-bHwD3sUE84Xwv4dH011gOKe1jul77M1S6ZFN9Tnq8pvZ48UMdY//vtES6fv7GRS5wXYT4iqxQPBluAiYAfkpmw==", "requires": {"csstype": "^3.1.3"}}, "@jridgewell/gen-mapping": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true}, "@jridgewell/source-map": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.5.tgz", "integrity": "sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==", "dev": true, "optional": true, "peer": true, "requires": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "dependencies": {"@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "dev": true, "optional": true, "peer": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}}}, "@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==", "dev": true, "requires": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "@rolldown/pluginutils": {"version": "1.0.0-beta.27", "resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.27.tgz", "integrity": "sha512-+d0F4MKMCbeVUJwG96uQ4SgAznZNSq93I3V+9NHA4OpvqG8mRCpGdKmK8l/dl02h2CCDHwW2FqilnTyDcAnqjA==", "dev": true}, "@rollup/rollup-android-arm-eabi": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz", "integrity": "sha512-<PERSON>rjy9awJl6rKbruhQDgivNbD1WuLb8xAclM4IR4cN5pHGAs2oIMMQJEiC3IC/9TZJ+QZkmZhlMO/6MBGxPidpw==", "dev": true, "optional": true}, "@rollup/rollup-android-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "integrity": "sha512-ss4YJwRt5I63454Rpj+mXCXicakdFmKnUNxr1dLK+5rv5FJgAxnN7s31a5VchRYxCFWdmnDWKd0wbAdTr0J5EA==", "dev": true, "optional": true}, "@rollup/rollup-darwin-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz", "integrity": "sha512-eKoL8ykZ7zz8MjgBenEF2OoTNFAPFz1/lyJ5UmmFSz5jW+7XbH1+MAgCVHy72aG59rbuQLcJeiMrP8qP5d/N0A==", "dev": true, "optional": true}, "@rollup/rollup-darwin-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz", "integrity": "sha512-SYwXJgaBYW33Wi/q4ubN+ldWC4DzQY62S4Ll2dgfr/dbPoF50dlQwEaEHSKrQdSjC6oIe1WgzosoaNoHCdNuMg==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-arm64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz", "integrity": "sha512-SV+U5sSo0yujrjzBF7/YidieK2iF6E7MdF6EbYxNz94lA+R0wKl3SiixGyG/9Klab6uNBIqsN7j4Y/Fya7wAjQ==", "dev": true, "optional": true}, "@rollup/rollup-freebsd-x64": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz", "integrity": "sha512-J7uCsiV13L/VOeHJBo5SjasKiGxJ0g+nQTrBkAsmQBIdil3KhPnSE9GnRon4ejX1XDdsmK/l30IYLiAaQEO0Cg==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-gnueabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz", "integrity": "sha512-gTJ/JnnjCMc15uwB10TTATBEhK9meBIY+gXP4s0sHD1zHOaIh4Dmy1X9wup18IiY9tTNk5gJc4yx9ctj/fjrIw==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm-musleabihf": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.43.0.tgz", "integrity": "sha512-ZJ3gZynL1LDSIvRfz0qXtTNs56n5DI2Mq+WACWZ7yGHFUEirHBRt7fyIk0NsCKhmRhn7WAcjgSkSVVxKlPNFFw==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz", "integrity": "sha512-8FnkipasmOOSSlfucGYEu58U8cxEdhziKjPD2FIa0ONVMxvl/hmONtX/7y4vGjdUhjcTHlKlDhw3H9t98fPvyA==", "dev": true, "optional": true}, "@rollup/rollup-linux-arm64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz", "integrity": "sha512-KPPyAdlcIZ6S9C3S2cndXDkV0Bb1OSMsX0Eelr2Bay4EsF9yi9u9uzc9RniK3mcUGCLhWY9oLr6er80P5DE6XA==", "dev": true, "optional": true}, "@rollup/rollup-linux-loongarch64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz", "integrity": "sha512-HPGDIH0/ZzAZjvtlXj6g+KDQ9ZMHfSP553za7o2Odegb/BEfwJcR0Sw0RLNpQ9nC6Gy8s+3mSS9xjZ0n3rhcYg==", "dev": true, "optional": true}, "@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz", "integrity": "sha512-gEmwbOws4U4GLAJDhhtSPWPXUzDfMRedT3hFMyRAvM9Mrnj+dJIFIeL7otsv2WF3D7GrV0GIewW0y28dOYWkmw==", "dev": true, "optional": true}, "@rollup/rollup-linux-riscv64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz", "integrity": "sha512-XXKvo2e+wFtXZF/9xoWohHg+MuRnvO29TI5Hqe9xwN5uN8NKUYy7tXUG3EZAlfchufNCTHNGjEx7uN78KsBo0g==", "dev": true, "optional": true}, "@rollup/rollup-linux-riscv64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz", "integrity": "sha512-ruf3hPWhjw6uDFsOAzmbNIvlXFXlBQ4nk57Sec8E8rUxs/AI4HD6xmiiasOOx/3QxS2f5eQMKTAwk7KHwpzr/Q==", "dev": true, "optional": true}, "@rollup/rollup-linux-s390x-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz", "integrity": "sha512-QmNIAqDiEMEvFV15rsSnjoSmO0+eJLoKRD9EAa9rrYNwO/XRCtOGM3A5A0X+wmG+XRrw9Fxdsw+LnyYiZWWcVw==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-gnu": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz", "integrity": "sha512-jAHr/S0iiBtFyzjhOkAics/2SrXE092qyqEg96e90L3t9Op8OTzS6+IX0Fy5wCt2+KqeHAkti+eitV0wvblEoQ==", "dev": true, "optional": true}, "@rollup/rollup-linux-x64-musl": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz", "integrity": "sha512-3yATWgdeXyuHtBhrLt98w+5fKurdqvs8B53LaoKD7P7H7FKOONLsBVMNl9ghPQZQuYcceV5CDyPfyfGpMWD9mQ==", "dev": true, "optional": true}, "@rollup/rollup-win32-arm64-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "integrity": "sha512-wVzXp2qDSCOpcBCT5WRWLmpJRIzv23valvcTwMHEobkjippNf+C3ys/+wf07poPkeNix0paTNemB2XrHr2TnGw==", "dev": true, "optional": true}, "@rollup/rollup-win32-ia32-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz", "integrity": "sha512-fYCTEyzf8d+7diCw8b+asvWDCLMjsCEA8alvtAutqJOJp/wL5hs1rWSqJ1vkjgW0L2NB4bsYJrpKkiIPRR9dvw==", "dev": true, "optional": true}, "@rollup/rollup-win32-x64-msvc": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.43.0.tgz", "integrity": "sha512-SnGhLiE5rlK0ofq8kzuDkM0g7FN1s5VYY+YSMTibP7CqShxCQvqtNxTARS4xX4PFJfHjG0ZQYX9iGzI3FQh5Aw==", "dev": true, "optional": true}, "@swc/helpers": {"version": "0.5.8", "resolved": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.8.tgz", "integrity": "sha512-lruDGw3pnfM3wmZHeW7JuhkGQaJjPyiKjxeGhdmfoOT53Ic9qb5JLDNaK2HUdl1zLDeX28H221UvKjfdvSLVMg==", "requires": {"tslib": "^2.4.0"}}, "@testing-library/dom": {"version": "10.1.0", "resolved": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.1.0.tgz", "integrity": "sha512-wdsYKy5zupPyLCW2Je5DLHSxSfbIp6h80WoHOQc+RPtmPGA52O9x5MJEkv92Sjonpq+poOAtUKhh1kBGAXBrNA==", "dev": true, "peer": true, "requires": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}}, "@testing-library/react": {"version": "16.3.0", "resolved": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "integrity": "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw==", "dev": true, "requires": {"@babel/runtime": "^7.12.5"}}, "@testing-library/user-event": {"version": "14.6.1", "resolved": "https://registry.npmjs.org/@testing-library/user-event/-/user-event-14.6.1.tgz", "integrity": "sha512-vq7fv0rnt+QTXgPxr5Hjc210p6YKq2kmdziLgnsZGgLJ9e6VAShx1pACLuRjd/AS/sr7phAR58OIIpf0LlmQNw==", "dev": true, "requires": {}}, "@types/aria-query": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz", "integrity": "sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==", "dev": true, "peer": true}, "@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "requires": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "@types/babel__generator": {"version": "7.6.4", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.4.tgz", "integrity": "sha512-tFkciB9j2K755yrTALxD44McOrk+gfpIpvC3sxHjRawj6PfnQxrse4Clq5y/Rq+G3mrBurMax/lG8Qn2t9mSsg==", "dev": true, "requires": {"@babel/types": "^7.0.0"}}, "@types/babel__template": {"version": "7.4.1", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.1.tgz", "integrity": "sha512-azBFKemX6kMg5Io+/rdGT0dkGreboUVR0Cdm3fz9QJWpaQGJRQXl7C+6hOTCZcMll7KFyEQpgbYI2lHdsS4U7g==", "dev": true, "requires": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "@types/babel__traverse": {"version": "7.18.0", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.18.0.tgz", "integrity": "sha512-v4Vwdko+pgymgS+A2UIaJru93zQd85vIGWObM5ekZNdXCKtDYqATlEYnWgfo86Q6I1Lh0oXnksDnMU1cwmlPDw==", "dev": true, "requires": {"@babel/types": "^7.3.0"}}, "@types/chai": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/@types/chai/-/chai-5.2.2.tgz", "integrity": "sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==", "dev": true, "requires": {"@types/deep-eql": "*"}}, "@types/deep-eql": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/deep-eql/-/deep-eql-4.0.2.tgz", "integrity": "sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==", "dev": true}, "@types/estree": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==", "dev": true}, "@types/node": {"version": "22.16.5", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.16.5.tgz", "integrity": "sha512-bJFoMATwIGaxxx8VJPeM8TonI8t579oRvgAuT8zFugJsJZgzqv0Fu8Mhp68iecjzG7cnN3mO2dJQ5uUM2EFrgQ==", "dev": true, "requires": {"undici-types": "~6.21.0"}}, "@types/prop-types": {"version": "15.7.5", "resolved": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "integrity": "sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w=="}, "@types/react": {"version": "18.3.23", "resolved": "https://registry.npmjs.org/@types/react/-/react-18.3.23.tgz", "integrity": "sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==", "requires": {"@types/prop-types": "*", "csstype": "^3.0.2"}}, "@types/react-dom": {"version": "18.3.7", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.3.7.tgz", "integrity": "sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==", "requires": {}}, "@types/react-test-renderer": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/@types/react-test-renderer/-/react-test-renderer-18.3.1.tgz", "integrity": "sha512-vAhnk0tG2eGa37lkU9+s5SoroCsRI08xnsWFiAXOuPH2jqzMbcXvKExXViPi1P5fIklDeCvXqyrdmipFaSkZrA==", "dev": true, "requires": {"@types/react": "^18"}}, "@vitejs/plugin-react": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.7.0.tgz", "integrity": "sha512-gUu9hwfWvvEDBBmgtAowQCojwZmJ5mcLn3aufeCsitijs3+f2NsrPtlAWIR6OPiqljl96GVCUbLe0HyqIpVaoA==", "dev": true, "requires": {"@babel/core": "^7.28.0", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.27", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}}, "@vitest/expect": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.4.tgz", "integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "dev": true, "requires": {"@types/chai": "^5.2.2", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "tinyrainbow": "^2.0.0"}}, "@vitest/mocker": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/mocker/-/mocker-3.2.4.tgz", "integrity": "sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==", "dev": true, "requires": {"@vitest/spy": "3.2.4", "estree-walker": "^3.0.3", "magic-string": "^0.30.17"}}, "@vitest/pretty-format": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.4.tgz", "integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "dev": true, "requires": {"tinyrainbow": "^2.0.0"}}, "@vitest/runner": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.4.tgz", "integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "dev": true, "requires": {"@vitest/utils": "3.2.4", "pathe": "^2.0.3", "strip-literal": "^3.0.0"}}, "@vitest/snapshot": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.4.tgz", "integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "dev": true, "requires": {"@vitest/pretty-format": "3.2.4", "magic-string": "^0.30.17", "pathe": "^2.0.3"}}, "@vitest/spy": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.4.tgz", "integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "dev": true, "requires": {"tinyspy": "^4.0.3"}}, "@vitest/utils": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.4.tgz", "integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "dev": true, "requires": {"@vitest/pretty-format": "3.2.4", "loupe": "^3.1.4", "tinyrainbow": "^2.0.0"}}, "acorn": {"version": "8.11.2", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.11.2.tgz", "integrity": "sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==", "dev": true, "optional": true, "peer": true}, "agent-base": {"version": "7.1.3", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz", "integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==", "dev": true}, "ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "peer": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "peer": true, "requires": {"color-convert": "^2.0.1"}}, "aria-query": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==", "dev": true, "peer": true, "requires": {"dequal": "^2.0.3"}}, "assertion-error": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.1.tgz", "integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==", "dev": true}, "browserslist": {"version": "4.25.0", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "dev": true, "requires": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}}, "buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true, "optional": true, "peer": true}, "cac": {"version": "6.7.14", "resolved": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==", "dev": true}, "caniuse-lite": {"version": "1.0.30001723", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz", "integrity": "sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==", "dev": true}, "chai": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/chai/-/chai-5.2.0.tgz", "integrity": "sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==", "dev": true, "requires": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}}, "chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "peer": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "check-error": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/check-error/-/check-error-2.1.1.tgz", "integrity": "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==", "dev": true}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "peer": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "peer": true}, "cssstyle": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/cssstyle/-/cssstyle-4.4.0.tgz", "integrity": "sha512-W0Y2HOXlPkb2yaKrCVRjinYKciu/qSLEmK0K9mcfDei3zwlnHFEHAs/Du3cIRwPqY+J4JsiBzUjoHyc8RsJ03A==", "dev": true, "requires": {"@asamuzakjp/css-color": "^3.2.0", "rrweb-cssom": "^0.8.0"}}, "csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="}, "data-urls": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/data-urls/-/data-urls-5.0.0.tgz", "integrity": "sha512-ZYP5VBHshaDAiVZxjbRVcFJpc+4xGgT0bK3vzy1HLN8jTO975HEbuYzZJcHoQEY5K1a0z8YayJkyVETa08eNTg==", "dev": true, "requires": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}}, "debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "requires": {"ms": "^2.1.3"}}, "decimal.js": {"version": "10.5.0", "resolved": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz", "integrity": "sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==", "dev": true}, "deep-eql": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.2.tgz", "integrity": "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==", "dev": true}, "dequal": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==", "dev": true, "peer": true}, "dom-accessibility-api": {"version": "0.5.14", "resolved": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.14.tgz", "integrity": "sha512-NMt+m9zFMPZe0JcY9gN224Qvk6qLIdqex29clBvc/y75ZBX9YA9wNK3frsYvu2DI1xcCIwxwnX+TlsJ2DSOADg==", "dev": true, "peer": true}, "dom-helpers": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==", "requires": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "electron-to-chromium": {"version": "1.5.167", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.167.tgz", "integrity": "sha512-LxcRvnYO5ez2bMOFpbuuVuAI5QNeY1ncVytE/KXaL6ZNfzX1yPlAO0nSOyIHx2fVAuUprMqPs/TdVhUFZy7SIQ==", "dev": true}, "embla-carousel": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.6.0.tgz", "integrity": "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA=="}, "embla-carousel-autoplay": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel-autoplay/-/embla-carousel-autoplay-8.6.0.tgz", "integrity": "sha512-OBu5G3nwaSXkZCo1A6LTaFMZ8EpkYbwIaH+bPqdBnDGQ2fh4+NbzjXjs2SktoPNKCtflfVMc75njaDHOYXcrsA==", "requires": {}}, "embla-carousel-fade": {"version": "8.6.0", "resolved": "https://registry.npmjs.org/embla-carousel-fade/-/embla-carousel-fade-8.6.0.tgz", "integrity": "sha512-qaYsx5mwCz72ZrjlsXgs1nKejSrW+UhkbOMwLgfRT7w2LtdEB03nPRI06GHuHv5ac2USvbEiX2/nAHctcDwvpg==", "requires": {}}, "entities": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/entities/-/entities-6.0.1.tgz", "integrity": "sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==", "dev": true}, "es-module-lexer": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==", "dev": true}, "esbuild": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.5.tgz", "integrity": "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==", "dev": true, "requires": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true}, "estree-walker": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "dev": true, "requires": {"@types/estree": "^1.0.0"}}, "expect-type": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.1.tgz", "integrity": "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==", "dev": true}, "fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "optional": true}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true}, "globrex": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/globrex/-/globrex-0.1.2.tgz", "integrity": "sha512-uHJgbwAMwNFf5mLst7IWLNg14x1CkeqglJb/K3doi4dw6q2IvAAmM/Y81kevy83wP+Sst+nutFTYOGg3d1lsxg==", "dev": true}, "has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "peer": true}, "html-encoding-sniffer": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-4.0.0.tgz", "integrity": "sha512-Y22oTqIU4uuPgEemfz7NDJz6OeKf12Lsu+QC+s3BVpda64lTiMYCyGwg5ki4vFxkMwQdeZDl2adZoqUgdFuTgQ==", "dev": true, "requires": {"whatwg-encoding": "^3.1.1"}}, "http-proxy-agent": {"version": "7.0.2", "resolved": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-7.0.2.tgz", "integrity": "sha512-T1gkAiYYDWYx3V5Bmyu7HcfcvL7mUrTWiM6yOfa3PIphViJ/gFPbvidQ+veqSOHci/PxBcDabeUNCzpOODJZig==", "dev": true, "requires": {"agent-base": "^7.1.0", "debug": "^4.3.4"}}, "https-proxy-agent": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "integrity": "sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==", "dev": true, "requires": {"agent-base": "^7.1.2", "debug": "4"}}, "iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}, "is-potential-custom-element-name": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", "integrity": "sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==", "dev": true}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "jsdom": {"version": "26.1.0", "resolved": "https://registry.npmjs.org/jsdom/-/jsdom-26.1.0.tgz", "integrity": "sha512-Cvc9WUhxSMEo4McES3P7oK3QaXldCfNWp7pl2NNeiIFlCoLr3kfq9kb1fxftiwk1FLV7CvpvDfonxtzUDeSOPg==", "dev": true, "requires": {"cssstyle": "^4.2.1", "data-urls": "^5.0.0", "decimal.js": "^10.5.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.16", "parse5": "^7.2.1", "rrweb-cssom": "^0.8.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^5.1.1", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.1.1", "ws": "^8.18.0", "xml-name-validator": "^5.0.0"}}, "jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true}, "json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true}, "keyborg": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/keyborg/-/keyborg-2.6.0.tgz", "integrity": "sha512-o5kvLbuTF+o326CMVYpjlaykxqYP9DphFQZ2ZpgrvBouyvOxyEB7oqe8nOLFpiV5VCtz0D3pt8gXQYWpLpBnmA=="}, "loose-envify": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "requires": {"js-tokens": "^3.0.0 || ^4.0.0"}}, "loupe": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/loupe/-/loupe-3.1.4.tgz", "integrity": "sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==", "dev": true}, "lz-string": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz", "integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==", "dev": true, "peer": true}, "magic-string": {"version": "0.30.17", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "dev": true, "requires": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true}, "nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true}, "node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true}, "nwsapi": {"version": "2.2.20", "resolved": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz", "integrity": "sha512-/ieB+mDe4MrrKMT8z+mQL8klXydZWGR5Dowt4RAGKbJ3kIGEx3X4ljUo+6V73IXtUPWgfOlU5B9MlGxFO5T+cA==", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="}, "parse5": {"version": "7.3.0", "resolved": "https://registry.npmjs.org/parse5/-/parse5-7.3.0.tgz", "integrity": "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==", "dev": true, "requires": {"entities": "^6.0.0"}}, "pathe": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "dev": true}, "pathval": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-2.0.0.tgz", "integrity": "sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==", "dev": true}, "picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true}, "picomatch": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz", "integrity": "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==", "dev": true}, "postcss": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "requires": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}}, "pretty-format": {"version": "27.5.1", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz", "integrity": "sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==", "dev": true, "peer": true, "requires": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "dependencies": {"ansi-styles": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "dev": true, "peer": true}}}, "prop-types": {"version": "15.8.1", "resolved": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "requires": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "dependencies": {"react-is": {"version": "16.13.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ=="}}}, "punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true}, "react": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "integrity": "sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==", "requires": {"loose-envify": "^1.1.0"}}, "react-dom": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "integrity": "sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==", "requires": {"loose-envify": "^1.1.0", "scheduler": "^0.23.2"}}, "react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w=="}, "react-refresh": {"version": "0.17.0", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==", "dev": true}, "react-shallow-renderer": {"version": "16.15.0", "resolved": "https://registry.npmjs.org/react-shallow-renderer/-/react-shallow-renderer-16.15.0.tgz", "integrity": "sha512-oScf2FqQ9LFVQgA73vr86xl2NaOIX73rh+YFqcOp68CWj56tSfgtGKrEbyhCj0rSijyG9M1CYprTh39fBi5hzA==", "dev": true, "requires": {"object-assign": "^4.1.1", "react-is": "^16.12.0 || ^17.0.0 || ^18.0.0"}}, "react-test-renderer": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-test-renderer/-/react-test-renderer-18.3.1.tgz", "integrity": "sha512-KkAgygexHUkQqtvvx/otwxtuFu5cVjfzTCtjXLH9boS19/Nbtg84zS7wIQn39G8IlrhThBpQsMKkq5ZHZIYFXA==", "dev": true, "requires": {"react-is": "^18.3.1", "react-shallow-renderer": "^16.15.0", "scheduler": "^0.23.2"}, "dependencies": {"react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "dev": true}}}, "react-transition-group": {"version": "4.4.5", "resolved": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==", "requires": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}}, "rollup": {"version": "4.43.0", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.43.0.tgz", "integrity": "sha512-wdN2Kd3Twh8MAEOEJZsuxuLKCsBEo4PVNLK6tQWAn10VhsVewQLzcucMgLolRlhFybGxfclbPeEYBaP6RvUFGg==", "dev": true, "requires": {"@rollup/rollup-android-arm-eabi": "4.43.0", "@rollup/rollup-android-arm64": "4.43.0", "@rollup/rollup-darwin-arm64": "4.43.0", "@rollup/rollup-darwin-x64": "4.43.0", "@rollup/rollup-freebsd-arm64": "4.43.0", "@rollup/rollup-freebsd-x64": "4.43.0", "@rollup/rollup-linux-arm-gnueabihf": "4.43.0", "@rollup/rollup-linux-arm-musleabihf": "4.43.0", "@rollup/rollup-linux-arm64-gnu": "4.43.0", "@rollup/rollup-linux-arm64-musl": "4.43.0", "@rollup/rollup-linux-loongarch64-gnu": "4.43.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-musl": "4.43.0", "@rollup/rollup-linux-s390x-gnu": "4.43.0", "@rollup/rollup-linux-x64-gnu": "4.43.0", "@rollup/rollup-linux-x64-musl": "4.43.0", "@rollup/rollup-win32-arm64-msvc": "4.43.0", "@rollup/rollup-win32-ia32-msvc": "4.43.0", "@rollup/rollup-win32-x64-msvc": "4.43.0", "@types/estree": "1.0.7", "fsevents": "~2.3.2"}}, "rrweb-cssom": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/rrweb-cssom/-/rrweb-cssom-0.8.0.tgz", "integrity": "sha512-guoltQEx+9aMf2gDZ0s62EcV8lsXR+0w8915TC3ITdn2YueuNjdAYh/levpU9nFaoChh9RUS5ZdQMrKfVEN9tw==", "dev": true}, "rtl-css-js": {"version": "1.16.1", "resolved": "https://registry.npmjs.org/rtl-css-js/-/rtl-css-js-1.16.1.tgz", "integrity": "sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==", "requires": {"@babel/runtime": "^7.1.2"}}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true}, "saxes": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/saxes/-/saxes-6.0.0.tgz", "integrity": "sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==", "dev": true, "requires": {"xmlchars": "^2.2.0"}}, "scheduler": {"version": "0.23.2", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "requires": {"loose-envify": "^1.1.0"}}, "siginfo": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz", "integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==", "dev": true}, "source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true}, "source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "optional": true, "peer": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "optional": true, "peer": true}}}, "stackback": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz", "integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==", "dev": true}, "std-env": {"version": "3.9.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz", "integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==", "dev": true}, "strip-literal": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-literal/-/strip-literal-3.0.0.tgz", "integrity": "sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==", "dev": true, "requires": {"js-tokens": "^9.0.1"}, "dependencies": {"js-tokens": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "dev": true}}}, "stylis": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/stylis/-/stylis-4.3.6.tgz", "integrity": "sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ=="}, "supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "peer": true, "requires": {"has-flag": "^4.0.0"}}, "symbol-tree": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz", "integrity": "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==", "dev": true}, "tabster": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/tabster/-/tabster-8.5.6.tgz", "integrity": "sha512-2vfrRGrx8O9BjdrtSlVA5fvpmbq5HQBRN13XFRg6LAvZ1Fr3QdBnswgT4YgFS5Bhoo5nxwgjRaRueI2Us/dv7g==", "requires": {"@rollup/rollup-linux-x64-gnu": "4.40.0", "keyborg": "2.6.0", "tslib": "^2.8.1"}, "dependencies": {"@rollup/rollup-linux-x64-gnu": {"version": "4.40.0", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.0.tgz", "integrity": "sha512-RcDGMtqF9EFN8i2RYN2W+64CdHruJ5rPqrlYw+cgM3uOVPSsnAQps7cpjXe9be/yDp8UC7VLoCoKC8J3Kn2FkQ==", "optional": true}}}, "terser": {"version": "5.24.0", "resolved": "https://registry.npmjs.org/terser/-/terser-5.24.0.tgz", "integrity": "sha512-ZpGR4Hy3+wBEzVEnHvstMvqpD/nABNelQn/z2r0fjVWGQsN3bpOLzQlqDxmb4CDZnXq5lpjnQ+mHQLAOpfM5iw==", "dev": true, "optional": true, "peer": true, "requires": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "dependencies": {"commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true, "optional": true, "peer": true}}}, "tinybench": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz", "integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==", "dev": true}, "tinyexec": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.2.tgz", "integrity": "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==", "dev": true}, "tinyglobby": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dev": true, "requires": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "dependencies": {"fdir": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "requires": {}}}}, "tinypool": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/tinypool/-/tinypool-1.1.1.tgz", "integrity": "sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==", "dev": true}, "tinyrainbow": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-2.0.0.tgz", "integrity": "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==", "dev": true}, "tinyspy": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.3.tgz", "integrity": "sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==", "dev": true}, "tldts": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts/-/tldts-6.1.86.tgz", "integrity": "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ==", "dev": true, "requires": {"tldts-core": "^6.1.86"}}, "tldts-core": {"version": "6.1.86", "resolved": "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.86.tgz", "integrity": "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA==", "dev": true}, "tough-cookie": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz", "integrity": "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A==", "dev": true, "requires": {"tldts": "^6.1.32"}}, "tr46": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz", "integrity": "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw==", "dev": true, "requires": {"punycode": "^2.3.1"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}, "typescript": {"version": "5.8.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true}, "undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "dev": true}, "update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "requires": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}}, "use-disposable": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/use-disposable/-/use-disposable-1.0.4.tgz", "integrity": "sha512-j83t6AMLWUyb5zwlTDqf6dP9LezM9R0yTbI/b6olmdaGtCKQUe9pgJWV6dRaaQLcozypjIEp4EmZr2DkZGKLSg==", "requires": {}}, "use-sync-external-store": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "integrity": "sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==", "requires": {}}, "vite": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/vite/-/vite-7.0.6.tgz", "integrity": "sha512-MHFiOENNBd+Bd9uvc8GEsIzdkn1JxMmEeYX35tI3fv0sJBUTfW5tQsoaOwuY4KhBI09A3dUJ/DXf2yxPVPUceg==", "dev": true, "requires": {"esbuild": "^0.25.0", "fdir": "^6.4.6", "fsevents": "~2.3.3", "picomatch": "^4.0.3", "postcss": "^8.5.6", "rollup": "^4.40.0", "tinyglobby": "^0.2.14"}, "dependencies": {"fdir": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "requires": {}}}}, "vite-node": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vite-node/-/vite-node-3.2.4.tgz", "integrity": "sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==", "dev": true, "requires": {"cac": "^6.7.14", "debug": "^4.4.1", "es-module-lexer": "^1.7.0", "pathe": "^2.0.3", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}}, "vite-tsconfig-paths": {"version": "5.1.4", "resolved": "https://registry.npmjs.org/vite-tsconfig-paths/-/vite-tsconfig-paths-5.1.4.tgz", "integrity": "sha512-cYj0LRuLV2c2sMqhqhGpaO3LretdtMn/BVX4cPLanIZuwwrkVl+lK84E/miEXkCHWXuq65rhNN4rXsBcOB3S4w==", "dev": true, "requires": {"debug": "^4.1.1", "globrex": "^0.1.2", "tsconfck": "^3.0.3"}, "dependencies": {"tsconfck": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/tsconfck/-/tsconfck-3.1.1.tgz", "integrity": "sha512-00eoI6WY57SvZEVjm13stEVE90VkEdJAFGgpFLTsZbJyW/LwFQ7uQxJHWpZ2hzSWgCPKc9AnBnNP+0X7o3hAmQ==", "dev": true, "requires": {}}}}, "vitest": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vitest/-/vitest-3.2.4.tgz", "integrity": "sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==", "dev": true, "requires": {"@types/chai": "^5.2.2", "@vitest/expect": "3.2.4", "@vitest/mocker": "3.2.4", "@vitest/pretty-format": "^3.2.4", "@vitest/runner": "3.2.4", "@vitest/snapshot": "3.2.4", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "debug": "^4.4.1", "expect-type": "^1.2.1", "magic-string": "^0.30.17", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tinypool": "^1.1.1", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0", "vite-node": "3.2.4", "why-is-node-running": "^2.3.0"}}, "w3c-xmlserializer": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-5.0.0.tgz", "integrity": "sha512-o8qghlI8NZHU1lLPrpi2+Uq7abh4GGPpYANlalzWxyWteJOCsr/P+oPBA49TOLu5FTZO4d3F9MnWJfiMo4BkmA==", "dev": true, "requires": {"xml-name-validator": "^5.0.0"}}, "web-vitals": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/web-vitals/-/web-vitals-5.0.3.tgz", "integrity": "sha512-4KmOFYxj7qT6RAdCH0SWwq8eKeXNhAFXR4PmgF6nrWFmrJ41n7lq3UCA6UK0GebQ4uu+XP8e8zGjaDO3wZlqTg=="}, "webidl-conversions": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz", "integrity": "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==", "dev": true}, "whatwg-encoding": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-3.1.1.tgz", "integrity": "sha512-6qN4hJdMwfYBtE3YBTTHhoeuUrDBPZmbQaxWAqSALV/MeEnR5z1xd8UKud2RAkFoPkmB+hli1TZSnyi84xz1vQ==", "dev": true, "requires": {"iconv-lite": "0.6.3"}}, "whatwg-mimetype": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-4.0.0.tgz", "integrity": "sha512-QaKxh0eNIi2mE9p2vEdzfagOKHCcj1pJ56EEHGQOVxp8r9/iszLUUV7v89x9O1p/T+NlTM5W7jW6+cz4Fq1YVg==", "dev": true}, "whatwg-url": {"version": "14.2.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz", "integrity": "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw==", "dev": true, "requires": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}}, "why-is-node-running": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz", "integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "dev": true, "requires": {"siginfo": "^2.0.0", "stackback": "0.0.2"}}, "ws": {"version": "8.18.2", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "dev": true, "requires": {}}, "xml-name-validator": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-5.0.0.tgz", "integrity": "sha512-EvGK8EJ3DhaHfbRlETOWAS5pO9MZITeauHKJyb8wyajUfQUenkIg2MvLDTZ4T/TgIcm3HU0TFBgWWboAZ30UHg==", "dev": true}, "xmlchars": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz", "integrity": "sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==", "dev": true}, "yaml": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/yaml/-/yaml-2.8.0.tgz", "integrity": "sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==", "dev": true, "optional": true, "peer": true}}}
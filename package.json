{"name": "diagnostics", "version": "0.1.0", "private": true, "dependencies": {"@fluentui/react-components": "^9.67.0", "@fluentui/react-theme": "^9.1.24", "react": "^18.3.1", "react-dom": "^18.3.1", "web-vitals": "^5.0.3"}, "scripts": {"build": "vite build", "dev": "vite", "start": "vite", "test": "vitest run", "test:watch": "vitest"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=22", "npm": ">=10"}, "devDependencies": {"@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.16.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-test-renderer": "^18.3.0", "@vitejs/plugin-react": "^4.7.0", "jsdom": "^26.1.0", "react-test-renderer": "^18.3.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}